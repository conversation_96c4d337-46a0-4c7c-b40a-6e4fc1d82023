The issue exists in the RedirectingDepositHook.callHook function where assets can remain unallocated after the distribution loop completes. RedirectingDepositHook.sol:8-26

Technical Analysis
The vulnerability occurs in the asset distribution logic:

Loop Logic: The function iterates through subvaults and attempts to deposit assets based on available capacity from riskManager.maxDeposit(). RedirectingDepositHook.sol:12-14

Partial Deposits: When a subvault has insufficient capacity, the hook deposits what it can and reduces the remaining assets. RedirectingDepositHook.sol:18-21

Critical Flaw: The loop exits via break when a subvault can handle all remaining assets, but there's no validation ensuring all assets were actually distributed. RedirectingDepositHook.sol:22-24

Exploitation Scenario
The vulnerability is exploitable when the cumulative capacity of all subvaults is less than the user's deposit amount:

User Gets Credited: The deposit queue already processes the user's deposit and credits them with shares. DepositQueue.sol:188-193

Assets Remain Unallocated: If subvaults lack sufficient total capacity, the remaining assets value will be > 0 after the loop completes, creating "ghost balance" that sits idle in the vault.

Unredeemable State: These leftover assets cannot be withdrawn by anyone since they're not tracked in any subvault's balance, despite users holding shares representing those assets.

Impact Assessment
This is a critical vulnerability because:

Users receive shares for assets that aren't actually deployed to any subvault
The unallocated assets become permanently locked in the vault
It violates the fundamental assumption that deposited assets are always properly allocated
Mitigation Validation
The suggested mitigation is correct and necessary: add a post-loop validation to ensure assets == 0, and revert with an "insufficient capacity" error if any assets remain unallocated.

Notes
This vulnerability demonstrates a classic asset accounting issue where the system fails to validate complete asset allocation during the deposit process. The maxDeposit function in the RiskManager correctly returns capacity limits RiskManager.sol:121-137 , but the deposit hook doesn't properly handle cases where total capacity is insufficient.


Real-World Exploitation Scenario
Consider a vault with two subvaults where a user deposits 1000 USDC:

Initial Setup
Subvault A: Has capacity for 300 USDC (based on risk limits)
Subvault B: Has capacity for 400 USDC (based on risk limits)
Total subvault capacity: 700 USDC
User deposit: 1000 USDC
Step-by-Step Exploitation
User Initiates Deposit: Alice deposits 1000 USDC into the vault through the deposit queue

Vault Credits User: The deposit queue transfers the 1000 USDC to the vault and credits Alice with shares representing 1000 USDC worth of value.

RedirectingDepositHook Executes: The hook attempts to distribute the 1000 USDC across subvaults

Asset Distribution Process:

Iteration 1: Subvault A can accept 300 USDC → deposits 300 USDC, remaining = 700 USDC
Iteration 2: Subvault B can accept 400 USDC → deposits 400 USDC, remaining = 300 USDC
Loop Ends: No more subvaults available, but 300 USDC remains unallocated
Ghost Balance Created: The function exits with assets = 300 still remaining, but there's no validation to ensure complete allocation.

Real Impact
Alice's Position: Holds shares worth 1000 USDC but only 700 USDC is actually deployed
Vault State: Contains 300 USDC sitting idle that cannot be withdrawn by anyone
System Inconsistency: The vault's accounting shows 1000 USDC in user shares but only 700 USDC is earning yield in subvaults
Test Case Evidence
The vulnerability is confirmed by examining the test cases in RedirectingDepositHook.t.sol:13-28 . The tests show successful asset distribution when capacity is sufficient, but there are no tests covering the insufficient capacity scenario that would expose this bug.

Why This Is Critical
This isn't a design choice but a critical bug because:

The maxDeposit function correctly calculates individual subvault limits
The system should either accept the full deposit or reject it entirely
Partial acceptance without proper validation creates an inconsistent state where user shares don't match deployed capital
The fix requires adding a post-loop check: require(assets == 0, "Insufficient capacity"); after the distribution loop completes.

