// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.25;

import "../Imports.sol";

/**
 * @title Vulnerability Bypass Analysis
 * @notice Tests to verify that no existing protective mechanisms can prevent the vulnerability
 * @dev This contract systematically tests all potential protective mechanisms
 */
contract VulnerabilityBypassAnalysis is Test {
    MockERC20 asset;
    MockVault vault;
    address subvault1;
    address subvault2;
    
    function setUp() external {
        asset = new MockERC20();
        vault = new MockVault();
        subvault1 = vm.createWallet("subvault1").addr;
        subvault2 = vm.createWallet("subvault2").addr;
    }

    /**
     * @notice Test 1: Verify no validation exists in RedirectingDepositHook
     * @dev Confirms that the hook doesn't validate complete asset allocation
     */
    function testBypass_NoValidationInHook() external {
        console.log("=== Bypass Test 1: No Validation in Hook ===");
        
        // Setup insufficient capacity scenario
        vault.addSubvault(subvault1, asset, 0 ether);
        vault.addRiskManager(0);
        MockRiskManager riskManager = MockRiskManager(address(vault.riskManager()));
        riskManager.setSubvaultLimit(subvault1, 100 ether);
        
        uint256 deposit = 200 ether;
        asset.mint(address(vault), deposit);
        
        console.log("Deposit:", deposit / 1 ether, "USDC");
        console.log("Capacity:", 100, "USDC");
        
        // The hook should validate that all assets are allocated, but it doesn't
        // This call should revert if proper validation existed, but it won't
        vault.afterDepositHookCall(address(asset), deposit);
        
        uint256 unallocated = asset.balanceOf(address(vault));
        console.log("Unallocated assets:", unallocated / 1 ether, "USDC");
        
        // If validation existed, this assertion would fail because the hook would revert
        assertEq(unallocated, 100 ether, "Hook allows unallocated assets without validation");
        
        console.log("CONFIRMED: RedirectingDepositHook has no validation for complete allocation");
    }

    /**
     * @notice Test 2: Verify RiskManager doesn't prevent the issue
     * @dev Tests that RiskManager.maxDeposit correctly reports limits but doesn't prevent the vulnerability
     */
    function testBypass_RiskManagerLimitsWork() external {
        console.log("=== Bypass Test 2: RiskManager Limits Work Correctly ===");
        
        vault.addSubvault(subvault1, asset, 0 ether);
        vault.addSubvault(subvault2, asset, 0 ether);
        vault.addRiskManager(0);
        MockRiskManager riskManager = MockRiskManager(address(vault.riskManager()));
        
        riskManager.setSubvaultLimit(subvault1, 150 ether);
        riskManager.setSubvaultLimit(subvault2, 250 ether);
        
        // Verify RiskManager correctly reports limits
        uint256 limit1 = riskManager.maxDeposit(subvault1, address(asset));
        uint256 limit2 = riskManager.maxDeposit(subvault2, address(asset));
        
        console.log("Subvault 1 limit:", limit1 / 1 ether, "USDC");
        console.log("Subvault 2 limit:", limit2 / 1 ether, "USDC");
        console.log("Total capacity:", (limit1 + limit2) / 1 ether, "USDC");
        
        assertEq(limit1, 150 ether, "RiskManager should report correct limit for subvault 1");
        assertEq(limit2, 250 ether, "RiskManager should report correct limit for subvault 2");
        
        // Test with deposit exceeding total capacity
        uint256 deposit = 500 ether; // Exceeds total capacity of 400
        asset.mint(address(vault), deposit);
        
        console.log("Deposit amount:", deposit / 1 ether, "USDC");
        
        // RiskManager limits work correctly, but the hook doesn't validate complete allocation
        vault.afterDepositHookCall(address(asset), deposit);
        
        uint256 allocated1 = asset.balanceOf(subvault1);
        uint256 allocated2 = asset.balanceOf(subvault2);
        uint256 unallocated = asset.balanceOf(address(vault));
        
        console.log("Allocated to subvault 1:", allocated1 / 1 ether, "USDC");
        console.log("Allocated to subvault 2:", allocated2 / 1 ether, "USDC");
        console.log("Unallocated:", unallocated / 1 ether, "USDC");
        
        // RiskManager limits are respected, but excess assets remain unallocated
        assertEq(allocated1, 150 ether, "Subvault 1 should receive exactly its limit");
        assertEq(allocated2, 250 ether, "Subvault 2 should receive exactly its limit");
        assertEq(unallocated, 100 ether, "Excess assets remain unallocated");
        
        console.log("CONFIRMED: RiskManager limits work correctly, but don't prevent unallocated assets");
    }

    /**
     * @notice Test 3: Verify no revert mechanism exists
     * @dev Confirms that the hook doesn't revert when assets can't be fully allocated
     */
    function testBypass_NoRevertMechanism() external {
        console.log("=== Bypass Test 3: No Revert Mechanism ===");
        
        vault.addSubvault(subvault1, asset, 0 ether);
        vault.addRiskManager(0);
        MockRiskManager riskManager = MockRiskManager(address(vault.riskManager()));
        riskManager.setSubvaultLimit(subvault1, 50 ether);
        
        uint256 deposit = 150 ether;
        asset.mint(address(vault), deposit);
        
        console.log("Testing hook with insufficient capacity...");
        console.log("Deposit:", deposit / 1 ether, "USDC");
        console.log("Capacity:", 50, "USDC");
        
        // This should revert if proper validation existed, but it won't
        try vault.afterDepositHookCall(address(asset), deposit) {
            console.log("Hook executed successfully (no revert)");
            
            uint256 unallocated = asset.balanceOf(address(vault));
            console.log("Unallocated assets:", unallocated / 1 ether, "USDC");
            
            assertEq(unallocated, 100 ether, "100 USDC should remain unallocated");
            console.log("CONFIRMED: Hook doesn't revert when assets can't be fully allocated");
        } catch {
            console.log("Hook reverted (unexpected)");
            fail("Hook should not revert - this indicates the vulnerability might be fixed");
        }
    }

    /**
     * @notice Test 4: Test edge case with all zero-capacity subvaults
     * @dev Verifies behavior when no subvaults can accept deposits
     */
    function testBypass_AllZeroCapacitySubvaults() external {
        console.log("=== Bypass Test 4: All Zero-Capacity Subvaults ===");
        
        vault.addSubvault(subvault1, asset, 0 ether);
        vault.addSubvault(subvault2, asset, 0 ether);
        vault.addRiskManager(0); // Default limit of 0 for all subvaults
        
        uint256 deposit = 100 ether;
        asset.mint(address(vault), deposit);
        
        console.log("All subvaults have zero capacity");
        console.log("Deposit:", deposit / 1 ether, "USDC");
        
        // Hook should handle this gracefully (or revert), but currently allows all assets to remain unallocated
        vault.afterDepositHookCall(address(asset), deposit);
        
        uint256 allocated1 = asset.balanceOf(subvault1);
        uint256 allocated2 = asset.balanceOf(subvault2);
        uint256 unallocated = asset.balanceOf(address(vault));
        
        console.log("Allocated to subvault 1:", allocated1 / 1 ether, "USDC");
        console.log("Allocated to subvault 2:", allocated2 / 1 ether, "USDC");
        console.log("Unallocated:", unallocated / 1 ether, "USDC");
        
        assertEq(allocated1, 0 ether, "Zero capacity subvault should receive nothing");
        assertEq(allocated2, 0 ether, "Zero capacity subvault should receive nothing");
        assertEq(unallocated, 100 ether, "All assets should remain unallocated");
        
        console.log("CONFIRMED: Hook allows 100% of assets to remain unallocated when no capacity exists");
    }

    /**
     * @notice Test 5: Verify no post-hook validation exists
     * @dev Tests that the calling context doesn't validate hook results
     */
    function testBypass_NoPostHookValidation() external {
        console.log("=== Bypass Test 5: No Post-Hook Validation ===");
        
        vault.addSubvault(subvault1, asset, 0 ether);
        vault.addRiskManager(0);
        MockRiskManager riskManager = MockRiskManager(address(vault.riskManager()));
        riskManager.setSubvaultLimit(subvault1, 75 ether);
        
        uint256 deposit = 200 ether;
        asset.mint(address(vault), deposit);
        
        uint256 vaultBalanceBefore = asset.balanceOf(address(vault));
        console.log("Vault balance before hook:", vaultBalanceBefore / 1 ether, "USDC");
        
        // Execute hook
        vault.afterDepositHookCall(address(asset), deposit);
        
        uint256 vaultBalanceAfter = asset.balanceOf(address(vault));
        uint256 allocated = asset.balanceOf(subvault1);
        
        console.log("Vault balance after hook:", vaultBalanceAfter / 1 ether, "USDC");
        console.log("Allocated to subvault:", allocated / 1 ether, "USDC");
        
        // The calling context (MockVault.afterDepositHookCall) doesn't validate that all assets were allocated
        assertEq(allocated, 75 ether, "75 USDC should be allocated");
        assertEq(vaultBalanceAfter, 125 ether, "125 USDC should remain in vault");
        
        // If post-hook validation existed, it would check that vaultBalanceAfter == vaultBalanceBefore - deposit
        // But no such validation exists
        console.log("CONFIRMED: No post-hook validation ensures complete asset allocation");
    }

    /**
     * @notice Test 6: Verify vulnerability persists across multiple hook calls
     * @dev Tests that the vulnerability compounds over multiple deposits
     */
    function testBypass_VulnerabilityCompounds() external {
        console.log("=== Bypass Test 6: Vulnerability Compounds ===");
        
        vault.addSubvault(subvault1, asset, 0 ether);
        vault.addRiskManager(0);
        MockRiskManager riskManager = MockRiskManager(address(vault.riskManager()));
        riskManager.setSubvaultLimit(subvault1, 60 ether);
        
        // First deposit
        uint256 deposit1 = 100 ether;
        asset.mint(address(vault), deposit1);
        vault.afterDepositHookCall(address(asset), deposit1);
        
        uint256 unallocated1 = asset.balanceOf(address(vault));
        console.log("After first deposit - Unallocated:", unallocated1 / 1 ether, "USDC");
        
        // Second deposit
        uint256 deposit2 = 80 ether;
        asset.mint(address(vault), deposit2);
        vault.afterDepositHookCall(address(asset), deposit2);
        
        uint256 unallocated2 = asset.balanceOf(address(vault));
        uint256 totalAllocated = asset.balanceOf(subvault1);
        
        console.log("After second deposit - Unallocated:", unallocated2 / 1 ether, "USDC");
        console.log("Total allocated to subvault:", totalAllocated / 1 ether, "USDC");
        
        // The subvault should still only have 60 USDC (its capacity limit)
        // But unallocated assets should compound: 40 + 80 = 120 USDC
        assertEq(totalAllocated, 60 ether, "Subvault should still only have 60 USDC");
        assertEq(unallocated2, 120 ether, "Unallocated assets should compound to 120 USDC");
        
        console.log("CONFIRMED: Vulnerability compounds - unallocated assets accumulate over time");
    }
}
