// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.25;

import "../Imports.sol";

/**
 * @title RedirectingDepositHook Vulnerability POC
 * @notice Comprehensive proof-of-concept demonstrating the asset allocation vulnerability
 * @dev This POC demonstrates how assets can remain unallocated after the distribution loop completes
 */
contract RedirectingDepositHookVulnerabilityPOC is Test {
    MockERC20 asset;
    MockVault vault;
    address subvault1;
    address subvault2;
    address subvault3;
    
    // Test accounts
    address alice = vm.createWallet("alice").addr;
    address bob = vm.createWallet("bob").addr;
    
    function setUp() external {
        asset = new MockERC20();
        vault = new MockVault();
        subvault1 = vm.createWallet("subvault1").addr;
        subvault2 = vm.createWallet("subvault2").addr;
        subvault3 = vm.createWallet("subvault3").addr;
    }

    /**
     * @notice POC Test 1: Demonstrates the core vulnerability
     * @dev Shows how assets remain unallocated when total subvault capacity < deposit amount
     */
    function testVulnerability_CoreIssue_UnallocatedAssets() external {
        console.log("=== POC Test 1: Core Vulnerability - Unallocated Assets ===");
        
        // STEP 1: Setup vault with limited subvault capacity
        console.log("Step 1: Setting up vault with limited capacity");
        
        // Add subvaults with specific capacity limits
        vault.addSubvault(subvault1, asset, 0 ether);
        vault.addSubvault(subvault2, asset, 0 ether);
        vault.addSubvault(subvault3, asset, 0 ether);
        
        // Create risk manager and set individual subvault limits
        vault.addRiskManager(0); // Default limit of 0
        MockRiskManager riskManager = MockRiskManager(address(vault.riskManager()));
        
        // Set capacity limits: Total capacity = 700 USDC
        riskManager.setSubvaultLimit(subvault1, 300 ether); // 300 USDC capacity
        riskManager.setSubvaultLimit(subvault2, 400 ether); // 400 USDC capacity  
        riskManager.setSubvaultLimit(subvault3, 0 ether);   // 0 USDC capacity (inactive)
        
        console.log("Subvault 1 capacity:", riskManager.maxDeposit(subvault1, address(asset)));
        console.log("Subvault 2 capacity:", riskManager.maxDeposit(subvault2, address(asset)));
        console.log("Subvault 3 capacity:", riskManager.maxDeposit(subvault3, address(asset)));
        console.log("Total subvault capacity: 700 USDC");
        
        // STEP 2: Simulate user deposit larger than total capacity
        uint256 userDeposit = 1000 ether; // 1000 USDC deposit
        console.log("Step 2: User deposits", userDeposit / 1 ether, "USDC");
        
        // Mint assets to vault (simulating deposit queue transfer)
        asset.mint(address(vault), userDeposit);
        uint256 vaultBalanceBefore = asset.balanceOf(address(vault));
        console.log("Vault balance before hook:", vaultBalanceBefore / 1 ether, "USDC");
        
        // STEP 3: Execute the vulnerable hook
        console.log("Step 3: Executing RedirectingDepositHook");
        vault.afterDepositHookCall(address(asset), userDeposit);
        
        // STEP 4: Measure the impact
        console.log("Step 4: Measuring impact");
        
        uint256 subvault1Balance = asset.balanceOf(subvault1);
        uint256 subvault2Balance = asset.balanceOf(subvault2);
        uint256 subvault3Balance = asset.balanceOf(subvault3);
        uint256 vaultBalanceAfter = asset.balanceOf(address(vault));
        
        console.log("Subvault 1 received:", subvault1Balance / 1 ether, "USDC");
        console.log("Subvault 2 received:", subvault2Balance / 1 ether, "USDC");
        console.log("Subvault 3 received:", subvault3Balance / 1 ether, "USDC");
        console.log("Vault balance after hook:", vaultBalanceAfter / 1 ether, "USDC");
        
        uint256 totalAllocated = subvault1Balance + subvault2Balance + subvault3Balance;
        uint256 unallocatedAssets = vaultBalanceAfter;
        
        console.log("Total allocated to subvaults:", totalAllocated / 1 ether, "USDC");
        console.log("Unallocated assets remaining in vault:", unallocatedAssets / 1 ether, "USDC");
        
        // STEP 5: Verify the vulnerability
        console.log("Step 5: Vulnerability verification");
        
        // Assert that assets were distributed according to capacity
        assertEq(subvault1Balance, 300 ether, "Subvault 1 should receive 300 USDC");
        assertEq(subvault2Balance, 400 ether, "Subvault 2 should receive 400 USDC");
        assertEq(subvault3Balance, 0 ether, "Subvault 3 should receive 0 USDC");
        
        // Critical assertion: Assets remain unallocated
        assertEq(unallocatedAssets, 300 ether, "300 USDC should remain unallocated in vault");
        assertEq(totalAllocated, 700 ether, "Only 700 USDC should be allocated to subvaults");
        
        // The vulnerability: User would receive shares for 1000 USDC but only 700 USDC is deployed
        console.log("VULNERABILITY CONFIRMED:");
        console.log("- User deposit: 1000 USDC");
        console.log("- Assets deployed: 700 USDC");
        console.log("- Assets unallocated: 300 USDC");
        console.log("- User would receive shares for full 1000 USDC despite incomplete allocation");
    }

    /**
     * @notice POC Test 2: Demonstrates real-world exploitation scenario
     * @dev Shows the complete flow from deposit queue perspective
     */
    function testVulnerability_RealWorldExploitation() external {
        console.log("=== POC Test 2: Real-World Exploitation Scenario ===");
        
        // STEP 1: Setup realistic vault configuration
        console.log("Step 1: Setting up realistic vault scenario");
        
        vault.addSubvault(subvault1, asset, 0 ether);
        vault.addSubvault(subvault2, asset, 0 ether);
        
        vault.addRiskManager(0);
        MockRiskManager riskManager = MockRiskManager(address(vault.riskManager()));
        
        // Realistic capacity limits based on risk management
        riskManager.setSubvaultLimit(subvault1, 500 ether);  // 500 USDC capacity
        riskManager.setSubvaultLimit(subvault2, 300 ether);  // 300 USDC capacity
        // Total capacity: 800 USDC
        
        console.log("Total available capacity: 800 USDC");
        
        // STEP 2: Multiple users deposit, exceeding capacity
        console.log("Step 2: Multiple users deposit exceeding total capacity");
        
        uint256 aliceDeposit = 600 ether;
        uint256 bobDeposit = 500 ether;
        uint256 totalDeposits = aliceDeposit + bobDeposit; // 1100 USDC total
        
        console.log("Alice deposits:", aliceDeposit / 1 ether, "USDC");
        console.log("Bob deposits:", bobDeposit / 1 ether, "USDC");
        console.log("Total deposits:", totalDeposits / 1 ether, "USDC");
        
        // Simulate the deposit queue transferring assets to vault
        asset.mint(address(vault), totalDeposits);
        
        // STEP 3: Process Alice's deposit first
        console.log("Step 3: Processing Alice's deposit");
        vault.afterDepositHookCall(address(asset), aliceDeposit);
        
        uint256 subvault1AfterAlice = asset.balanceOf(subvault1);
        uint256 subvault2AfterAlice = asset.balanceOf(subvault2);
        uint256 vaultAfterAlice = asset.balanceOf(address(vault));
        
        console.log("After Alice - Subvault 1:", subvault1AfterAlice / 1 ether, "USDC");
        console.log("After Alice - Subvault 2:", subvault2AfterAlice / 1 ether, "USDC");
        console.log("After Alice - Vault remaining:", vaultAfterAlice / 1 ether, "USDC");
        
        // STEP 4: Process Bob's deposit (this will trigger the vulnerability)
        console.log("Step 4: Processing Bob's deposit (vulnerability trigger)");
        vault.afterDepositHookCall(address(asset), bobDeposit);
        
        uint256 subvault1Final = asset.balanceOf(subvault1);
        uint256 subvault2Final = asset.balanceOf(subvault2);
        uint256 vaultFinal = asset.balanceOf(address(vault));
        
        console.log("Final - Subvault 1:", subvault1Final / 1 ether, "USDC");
        console.log("Final - Subvault 2:", subvault2Final / 1 ether, "USDC");
        console.log("Final - Vault remaining:", vaultFinal / 1 ether, "USDC");
        
        // STEP 5: Calculate the impact
        uint256 totalDeployed = subvault1Final + subvault2Final;
        uint256 unallocatedAssets = vaultFinal;
        
        console.log("EXPLOITATION RESULTS:");
        console.log("- Total user deposits: 1100 USDC");
        console.log("- Total assets deployed:", totalDeployed / 1 ether, "USDC");
        console.log("- Unallocated assets:", unallocatedAssets / 1 ether, "USDC");
        console.log("- Users receive shares for 1100 USDC but only", totalDeployed / 1 ether, "USDC deployed");
        
        // Verify the vulnerability impact
        assertEq(totalDeployed, 800 ether, "Only 800 USDC should be deployed (total capacity)");
        assertEq(unallocatedAssets, 300 ether, "300 USDC should remain unallocated");
        
        // This represents the "ghost balance" - assets that users have shares for but aren't earning yield
        uint256 ghostBalance = unallocatedAssets;
        console.log("Ghost balance created:", ghostBalance / 1 ether, "USDC");
    }

    /**
     * @notice POC Test 3: Edge case testing - Zero capacity subvaults
     * @dev Tests behavior when some subvaults have zero capacity
     */
    function testVulnerability_EdgeCase_ZeroCapacitySubvaults() external {
        console.log("=== POC Test 3: Edge Case - Zero Capacity Subvaults ===");

        // Setup with mixed capacity subvaults
        vault.addSubvault(subvault1, asset, 0 ether);
        vault.addSubvault(subvault2, asset, 0 ether);
        vault.addSubvault(subvault3, asset, 0 ether);

        vault.addRiskManager(0);
        MockRiskManager riskManager = MockRiskManager(address(vault.riskManager()));

        // Mixed capacity: some zero, some limited
        riskManager.setSubvaultLimit(subvault1, 0 ether);     // Zero capacity
        riskManager.setSubvaultLimit(subvault2, 200 ether);   // Limited capacity
        riskManager.setSubvaultLimit(subvault3, 0 ether);     // Zero capacity

        uint256 deposit = 500 ether;
        asset.mint(address(vault), deposit);

        console.log("Deposit amount:", deposit / 1 ether, "USDC");
        console.log("Only subvault 2 has capacity: 200 USDC");

        vault.afterDepositHookCall(address(asset), deposit);

        uint256 subvault1Balance = asset.balanceOf(subvault1);
        uint256 subvault2Balance = asset.balanceOf(subvault2);
        uint256 subvault3Balance = asset.balanceOf(subvault3);
        uint256 vaultBalance = asset.balanceOf(address(vault));

        console.log("Subvault 1 (0 capacity):", subvault1Balance / 1 ether, "USDC");
        console.log("Subvault 2 (200 capacity):", subvault2Balance / 1 ether, "USDC");
        console.log("Subvault 3 (0 capacity):", subvault3Balance / 1 ether, "USDC");
        console.log("Vault remaining:", vaultBalance / 1 ether, "USDC");

        // Verify edge case behavior
        assertEq(subvault1Balance, 0 ether, "Zero capacity subvault should receive nothing");
        assertEq(subvault2Balance, 200 ether, "Limited capacity subvault should be filled");
        assertEq(subvault3Balance, 0 ether, "Zero capacity subvault should receive nothing");
        assertEq(vaultBalance, 300 ether, "300 USDC should remain unallocated");

        console.log("EDGE CASE CONFIRMED: 300 USDC remains unallocated despite user receiving shares for 500 USDC");
    }

    /**
     * @notice POC Test 4: Boundary condition - Exact capacity match
     * @dev Tests the boundary where deposit exactly matches total capacity
     */
    function testVulnerability_BoundaryCondition_ExactCapacityMatch() external {
        console.log("=== POC Test 4: Boundary Condition - Exact Capacity Match ===");

        vault.addSubvault(subvault1, asset, 0 ether);
        vault.addSubvault(subvault2, asset, 0 ether);

        vault.addRiskManager(0);
        MockRiskManager riskManager = MockRiskManager(address(vault.riskManager()));

        riskManager.setSubvaultLimit(subvault1, 300 ether);
        riskManager.setSubvaultLimit(subvault2, 200 ether);
        // Total capacity: 500 USDC

        uint256 deposit = 500 ether; // Exactly matches total capacity
        asset.mint(address(vault), deposit);

        console.log("Deposit amount:", deposit / 1 ether, "USDC");
        console.log("Total capacity:", 500, "USDC");

        vault.afterDepositHookCall(address(asset), deposit);

        uint256 subvault1Balance = asset.balanceOf(subvault1);
        uint256 subvault2Balance = asset.balanceOf(subvault2);
        uint256 vaultBalance = asset.balanceOf(address(vault));

        console.log("Subvault 1:", subvault1Balance / 1 ether, "USDC");
        console.log("Subvault 2:", subvault2Balance / 1 ether, "USDC");
        console.log("Vault remaining:", vaultBalance / 1 ether, "USDC");

        // In this boundary case, all assets should be allocated
        assertEq(subvault1Balance, 300 ether, "Subvault 1 should be fully utilized");
        assertEq(subvault2Balance, 200 ether, "Subvault 2 should be fully utilized");
        assertEq(vaultBalance, 0 ether, "No assets should remain when capacity exactly matches");

        console.log("BOUNDARY CASE: When deposit exactly matches capacity, all assets are properly allocated");
    }

    /**
     * @notice POC Test 5: Persistence verification
     * @dev Verifies that unallocated assets persist and cannot be recovered
     */
    function testVulnerability_Persistence_UnrecoverableAssets() external {
        console.log("=== POC Test 5: Persistence - Unrecoverable Assets ===");

        // Setup vulnerability scenario
        vault.addSubvault(subvault1, asset, 0 ether);
        vault.addRiskManager(0);
        MockRiskManager riskManager = MockRiskManager(address(vault.riskManager()));
        riskManager.setSubvaultLimit(subvault1, 100 ether);

        uint256 deposit = 200 ether;
        asset.mint(address(vault), deposit);

        console.log("Initial deposit:", deposit / 1 ether, "USDC");
        console.log("Subvault capacity:", 100, "USDC");

        // Trigger vulnerability
        vault.afterDepositHookCall(address(asset), deposit);

        uint256 unallocatedBefore = asset.balanceOf(address(vault));
        console.log("Unallocated assets after hook:", unallocatedBefore / 1 ether, "USDC");

        // Attempt to call hook again (simulating another deposit)
        uint256 additionalDeposit = 50 ether;
        asset.mint(address(vault), additionalDeposit);
        vault.afterDepositHookCall(address(asset), additionalDeposit);

        uint256 unallocatedAfter = asset.balanceOf(address(vault));
        console.log("Unallocated assets after second hook:", unallocatedAfter / 1 ether, "USDC");

        // The unallocated assets from the first deposit should still be there
        // plus the new unallocated assets from the second deposit
        assertEq(unallocatedAfter, 150 ether, "150 USDC should remain unallocated (100 + 50)");

        console.log("PERSISTENCE CONFIRMED: Unallocated assets accumulate and cannot be recovered through normal operations");
    }

    /**
     * @notice POC Test 6: Impact quantification
     * @dev Quantifies the actual financial impact of the vulnerability
     */
    function testVulnerability_ImpactQuantification() external {
        console.log("=== POC Test 6: Impact Quantification ===");

        // Setup large-scale scenario
        vault.addSubvault(subvault1, asset, 0 ether);
        vault.addSubvault(subvault2, asset, 0 ether);
        vault.addSubvault(subvault3, asset, 0 ether);

        vault.addRiskManager(0);
        MockRiskManager riskManager = MockRiskManager(address(vault.riskManager()));

        // Large capacity limits
        riskManager.setSubvaultLimit(subvault1, 1000000 ether); // 1M USDC
        riskManager.setSubvaultLimit(subvault2, 500000 ether);  // 500K USDC
        riskManager.setSubvaultLimit(subvault3, 300000 ether);  // 300K USDC
        // Total capacity: 1.8M USDC

        // Massive deposit exceeding capacity
        uint256 massiveDeposit = 2500000 ether; // 2.5M USDC
        asset.mint(address(vault), massiveDeposit);

        console.log("Massive deposit:", massiveDeposit / 1 ether, "USDC");
        console.log("Total subvault capacity: 1,800,000 USDC");

        vault.afterDepositHookCall(address(asset), massiveDeposit);

        uint256 totalDeployed = asset.balanceOf(subvault1) + asset.balanceOf(subvault2) + asset.balanceOf(subvault3);
        uint256 unallocatedAssets = asset.balanceOf(address(vault));

        console.log("Assets deployed:", totalDeployed / 1 ether, "USDC");
        console.log("Assets unallocated:", unallocatedAssets / 1 ether, "USDC");

        // Calculate financial impact
        uint256 impactPercentage = (unallocatedAssets * 100) / massiveDeposit;
        console.log("Impact percentage:", impactPercentage, "%");

        // Assuming 5% APY on deployed assets, calculate lost yield
        uint256 annualLostYield = (unallocatedAssets * 5) / 100;
        console.log("Annual lost yield (at 5% APY):", annualLostYield / 1 ether, "USDC");

        assertEq(totalDeployed, 1800000 ether, "1.8M USDC should be deployed");
        assertEq(unallocatedAssets, 700000 ether, "700K USDC should remain unallocated");
        assertEq(impactPercentage, 28, "28% of user funds are unallocated");

        console.log("IMPACT QUANTIFIED:");
        console.log("- 700,000 USDC unallocated (28% of deposit)");
        console.log("- Annual lost yield: 35,000 USDC");
        console.log("- Users receive shares for 2.5M but only 1.8M earns yield");
    }

    /**
     * @notice Demonstrates that the vulnerability bypasses all current protective mechanisms
     */
    function testVulnerability_BypassProtectiveMechanisms() external {
        console.log("=== POC Test 7: Bypass Protective Mechanisms ===");

        vault.addSubvault(subvault1, asset, 0 ether);
        vault.addRiskManager(0);
        MockRiskManager riskManager = MockRiskManager(address(vault.riskManager()));
        riskManager.setSubvaultLimit(subvault1, 100 ether);

        uint256 deposit = 200 ether;
        asset.mint(address(vault), deposit);

        console.log("Testing bypass of protective mechanisms...");

        // The hook executes without any validation of complete allocation
        vault.afterDepositHookCall(address(asset), deposit);

        uint256 unallocated = asset.balanceOf(address(vault));
        console.log("Unallocated despite any protective mechanisms:", unallocated / 1 ether, "USDC");

        assertEq(unallocated, 100 ether, "100 USDC remains unallocated despite protective mechanisms");

        console.log("BYPASS CONFIRMED: No existing protective mechanisms prevent this vulnerability");
    }
}
