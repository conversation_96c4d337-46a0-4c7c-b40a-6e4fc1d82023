// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.25;

contract MockRiskManager {
    uint256 internal _limit;
    mapping(address => uint256) internal _subvaultLimits;
    mapping(address => bool) internal _hasCustomLimit;

    constructor(uint256 limit) {
        _limit = limit;
    }

    function maxDeposit(address subvault, address) external view returns (uint256) {
        if (_hasCustomLimit[subvault]) {
            return _subvaultLimits[subvault];
        }
        return _limit;
    }

    function setSubvaultLimit(address subvault, uint256 limit) external {
        _subvaultLimits[subvault] = limit;
        _hasCustomLimit[subvault] = true;
    }

    function test() external {}
}
