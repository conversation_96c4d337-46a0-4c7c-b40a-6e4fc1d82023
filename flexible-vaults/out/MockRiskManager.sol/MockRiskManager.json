{"abi": [{"type": "constructor", "inputs": [{"name": "limit", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "maxDeposit", "inputs": [{"name": "subvault", "type": "address", "internalType": "address"}, {"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "setSubvaultLimit", "inputs": [{"name": "subvault", "type": "address", "internalType": "address"}, {"name": "limit", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x6080604052348015600e575f80fd5b506040516101f13803806101f1833981016040819052602b916031565b5f556047565b5f602082840312156040575f80fd5b5051919050565b61019d806100545f395ff3fe608060405234801561000f575f80fd5b506004361061003f575f3560e01c806308e59e0d146100435780637601687014610086578063f8a8fd6d14610084575b5f80fd5b61008461005136600461010e565b6001600160a01b039091165f9081526001602081815260408084209490945560029052919020805460ff19169091179055565b005b610099610094366004610136565b6100ab565b60405190815260200160405180910390f35b6001600160a01b0382165f9081526002602052604081205460ff16156100e957506001600160a01b0382165f908152600160205260409020546100ed565b505f545b92915050565b80356001600160a01b0381168114610109575f80fd5b919050565b5f806040838503121561011f575f80fd5b610128836100f3565b946020939093013593505050565b5f8060408385031215610147575f80fd5b610150836100f3565b915061015e602084016100f3565b9050925092905056fea26469706673582212204cd0e143be106d2ad63ba10cade1388c997574eb25a42f3d619cc10d1c08157e64736f6c63430008190033", "sourceMap": "62:645:148:-:0;;;236:58;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;273:6;:14;62:645;;14:184:151;84:6;137:2;125:9;116:7;112:23;108:32;105:52;;;153:1;150;143:12;105:52;-1:-1:-1;176:16:151;;14:184;-1:-1:-1;14:184:151:o;:::-;62:645:148;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x608060405234801561000f575f80fd5b506004361061003f575f3560e01c806308e59e0d146100435780637601687014610086578063f8a8fd6d14610084575b5f80fd5b61008461005136600461010e565b6001600160a01b039091165f9081526001602081815260408084209490945560029052919020805460ff19169091179055565b005b610099610094366004610136565b6100ab565b60405190815260200160405180910390f35b6001600160a01b0382165f9081526002602052604081205460ff16156100e957506001600160a01b0382165f908152600160205260409020546100ed565b505f545b92915050565b80356001600160a01b0381168114610109575f80fd5b919050565b5f806040838503121561011f575f80fd5b610128836100f3565b946020939093013593505050565b5f8060408385031215610147575f80fd5b610150836100f3565b915061015e602084016100f3565b9050925092905056fea26469706673582212204cd0e143be106d2ad63ba10cade1388c997574eb25a42f3d619cc10d1c08157e64736f6c63430008190033", "sourceMap": "62:645:148:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;512:160;;;;;;:::i;:::-;-1:-1:-1;;;;;590:25:148;;;;;;;:15;:25;;;;;;;;:33;;;;633:15;:25;;;;;:32;;-1:-1:-1;;633:32:148;;;;;;512:160;;;300:206;;;;;;:::i;:::-;;:::i;:::-;;;862:25:151;;;850:2;835:18;300:206:148;;;;;;;;-1:-1:-1;;;;;393:25:148;;370:7;393:25;;;:15;:25;;;;;;;;389:88;;;-1:-1:-1;;;;;;441:25:148;;;;;;:15;:25;;;;;;434:32;;389:88;-1:-1:-1;493:6:148;;300:206;;;;;:::o;14:173:151:-;82:20;;-1:-1:-1;;;;;131:31:151;;121:42;;111:70;;177:1;174;167:12;111:70;14:173;;;:::o;192:254::-;260:6;268;321:2;309:9;300:7;296:23;292:32;289:52;;;337:1;334;327:12;289:52;360:29;379:9;360:29;:::i;:::-;350:39;436:2;421:18;;;;408:32;;-1:-1:-1;;;192:254:151:o;451:260::-;519:6;527;580:2;568:9;559:7;555:23;551:32;548:52;;;596:1;593;586:12;548:52;619:29;638:9;619:29;:::i;:::-;609:39;;667:38;701:2;690:9;686:18;667:38;:::i;:::-;657:48;;451:260;;;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"maxDeposit(address,address)": "76016870", "setSubvaultLimit(address,uint256)": "08e59e0d", "test()": "f8a8fd6d"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"limit\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"subvault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"maxDeposit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"subvault\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"limit\",\"type\":\"uint256\"}],\"name\":\"setSubvaultLimit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/mocks/MockRiskManager.sol\":\"MockRiskManager\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"test/mocks/MockRiskManager.sol\":{\"keccak256\":\"0x6f8cbdaff1f0436750b2a61cf7def9d279416c778c57e21f90e500e9b99da02a\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://9dbc697b4328da1f086bb90fe643a685f55d076727589df2509a883e4668876d\",\"dweb:/ipfs/QmdK5nkndDqnwb6yoPd1WkhpNs4R1gSrXQpksVPBXbGbe5\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint256", "name": "limit", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "subvault", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "maxDeposit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "subvault", "type": "address"}, {"internalType": "uint256", "name": "limit", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setSubvaultLimit"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/mocks/MockRiskManager.sol": "MockRiskManager"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"test/mocks/MockRiskManager.sol": {"keccak256": "0x6f8cbdaff1f0436750b2a61cf7def9d279416c778c57e21f90e500e9b99da02a", "urls": ["bzz-raw://9dbc697b4328da1f086bb90fe643a685f55d076727589df2509a883e4668876d", "dweb:/ipfs/QmdK5nkndDqnwb6yoPd1WkhpNs4R1gSrXQpksVPBXbGbe5"], "license": "BUSL-1.1"}}, "version": 1}, "id": 148}