{"abi": [{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "WETH", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "addLidoDepositHook", "inputs": [{"name": "nextHook", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "addRiskManager", "inputs": [{"name": "limit", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "addSubvault", "inputs": [{"name": "subvault_", "type": "address", "internalType": "address"}, {"name": "asset_", "type": "address", "internalType": "contract MockERC20"}, {"name": "amount_", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "afterDepositHookCall", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}, {"name": "assets", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "beforeRedeemHookCall", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}, {"name": "assets", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getLiquidAssetsCall", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "hookPullAssets", "inputs": [{"name": "subvault_", "type": "address", "internalType": "address"}, {"name": "asset", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "hookPushAssets", "inputs": [{"name": "subvault_", "type": "address", "internalType": "address"}, {"name": "asset", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "lidoDepositHook", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "lidoDepositHookCall", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}, {"name": "assets", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "pullAssets", "inputs": [{"name": "subvault_", "type": "address", "internalType": "address"}, {"name": "asset", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "pushAssets", "inputs": [{"name": "subvault_", "type": "address", "internalType": "address"}, {"name": "asset", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "redeemH<PERSON>", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "redirectingDepositHook", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "riskManager", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IRiskManager"}], "stateMutability": "view"}, {"type": "function", "name": "subvaultAt", "inputs": [{"name": "index", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "subvaults", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "test", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "wstETH", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "FailedCall", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "352:2681:149:-:0;;;590:42;556:76;;670:42;638:74;;719:146;;;;;;;;;;764:21;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;743:10:149;:43;;-1:-1:-1;;;;;;743:43:149;-1:-1:-1;;;;;743:43:149;;;;;;;;;;829:28;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;796:22:149;:62;;-1:-1:-1;;;;;;796:62:149;-1:-1:-1;;;;;796:62:149;;;;;;;;;;352:2681;;;;;;;;;;:::o;:::-;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "352:2681:149:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2720:134;;;;;;;;;;-1:-1:-1;2720:134:149;;;;;:::i;:::-;;:::i;:::-;;1388:176;;;;;;;;;;-1:-1:-1;1388:176:149;;;;;:::i;:::-;;:::i;484:30::-;;;;;;;;;;-1:-1:-1;484:30:149;;;;-1:-1:-1;;;;;484:30:149;;;;;;-1:-1:-1;;;;;1095:32:151;;;1077:51;;1065:2;1050:18;484:30:149;;;;;;;;2102:110;;;;;;;;;;-1:-1:-1;2192:12:149;;-1:-1:-1;;;;;2192:12:149;2102:110;;556:76;;;;;;;;;;;;;;;410:25;;;;;;;;;;-1:-1:-1;410:25:149;;;;-1:-1:-1;;;;;410:25:149;;;1570:188;;;;;;;;;;-1:-1:-1;1570:188:149;;;;;:::i;:::-;;:::i;906:142::-;;;;;;;;;;-1:-1:-1;906:142:149;;;;;:::i;:::-;;:::i;1054:207::-;;;;;;;;;;-1:-1:-1;1054:207:149;;;;;:::i;:::-;;:::i;2316:106::-;;;;;;;;;;-1:-1:-1;2316:106:149;;;;;:::i;:::-;;:::i;2218:92::-;;;;;;;;;;-1:-1:-1;2262:7:149;2288:15;2218:92;;;2432:25:151;;;2420:2;2405:18;2218:92:149;2286:177:151;638:74:149;;;;;;;;;;;;;;;441:37;;;;;;;;;;-1:-1:-1;441:37:149;;;;-1:-1:-1;;;;;441:37:149;;;1267:115;;;;;;;;;;-1:-1:-1;1267:115:149;;;;;:::i;:::-;;:::i;1764:180::-;;;;;;;;;;-1:-1:-1;1764:180:149;;;;;:::i;:::-;;:::i;2572:142::-;;;;;;;;;;-1:-1:-1;2572:142:149;;;;;:::i;:::-;;:::i;1950:146::-;;;;;;;;;;-1:-1:-1;1950:146:149;;;;;:::i;:::-;;:::i;3004:27::-;;;;;;;;;2720:134;2808:39;;-1:-1:-1;;;2808:39:149;;-1:-1:-1;;;;;2660:32:151;;;2808:39:149;;;2642:51:151;2709:18;;;2702:34;;;2808:21:149;;;;;2615:18:151;;2808:39:149;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2720:134;;;:::o;1388:176::-;1497:10;;1509:47;;-1:-1:-1;;;;;2660:32:151;;;1509:47:149;;;2642:51:151;2709:18;;;2702:34;;;1468:89:149;;1497:10;;2615:18:151;;1509:47:149;;;;-1:-1:-1;;1509:47:149;;;;;;;;;;;;;;-1:-1:-1;;;;;1509:47:149;-1:-1:-1;;;1509:47:149;;;1468:28;:89::i;:::-;;1388:176;;:::o;1570:188::-;1679:22;;1703:47;;-1:-1:-1;;;;;2660:32:151;;;1703:47:149;;;2642:51:151;2709:18;;;2702:34;;;1650:101:149;;1679:22;;2615:18:151;;1703:47:149;2468:274:151;906:142:149;1017:6;1025:4;1031:8;997:43;;;;;:::i;:::-;-1:-1:-1;;;;;3005:15:151;;;2987:34;;3057:15;;;3052:2;3037:18;;3030:43;3109:15;;;3104:2;3089:18;;3082:43;2937:2;2922:18;997:43:149;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;971:15:149;:70;;-1:-1:-1;;;;;;971:70:149;-1:-1:-1;;;;;971:70:149;;;;;;;;;;-1:-1:-1;906:142:149:o;1054:207::-;1148:8;:24;;;;;;;;;;;;;;-1:-1:-1;;;;;;1148:24:149;-1:-1:-1;;;;;1148:24:149;;;;;1186:11;;1182:73;;1213:31;;-1:-1:-1;;;1213:31:149;;-1:-1:-1;;;;;2660:32:151;;;1213:31:149;;;2642:51:151;2709:18;;;2702:34;;;1213:11:149;;;;;2615:18:151;;1213:31:149;2468:274:151;2316:106:149;2374:7;2400:8;2409:5;2400:15;;;;;;;;:::i;:::-;;;;;;;;;;;-1:-1:-1;;;;;2400:15:149;;2316:106;-1:-1:-1;;2316:106:149:o;1267:115::-;1368:5;1348:26;;;;;:::i;:::-;2432:25:151;;;2420:2;2405:18;1348:26:149;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;1325:12:149;:50;;-1:-1:-1;;;;;;1325:50:149;-1:-1:-1;;;;;1325:50:149;;;;;;;;;;-1:-1:-1;1267:115:149:o;1764:180::-;1872:15;;1889:47;;-1:-1:-1;;;;;2660:32:151;;;1889:47:149;;;2642:51:151;2709:18;;;2702:34;;;1843:94:149;;1872:15;;2615:18:151;;1889:47:149;2468:274:151;2572:142:149;2664:43;;-1:-1:-1;;;2664:43:149;;-1:-1:-1;;;;;2660:32:151;;;2664:43:149;;;2642:51:151;2709:18;;;2702:34;;;2664:25:149;;;;;2615:18:151;;2664:43:149;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;2572:142;;;:::o;1950:146::-;2055:10;;2043:46;;-1:-1:-1;;;2043:46:149;;-1:-1:-1;;;;;1095:32:151;;;2043:46:149;;;1077:51:151;2017:7:149;;2055:10;;2043:39;;1050:18:151;;2043:46:149;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2036:53;1950:146;-1:-1:-1;;1950:146:149:o;3916:253:53:-;3999:12;4024;4038:23;4065:6;-1:-1:-1;;;;;4065:19:53;4085:4;4065:25;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4023:67;;;;4107:55;4134:6;4142:7;4151:10;4107:26;:55::i;:::-;4100:62;3916:253;-1:-1:-1;;;;;3916:253:53:o;4437:582::-;4581:12;4610:7;4605:408;;4633:19;4641:10;4633:7;:19::i;:::-;4605:408;;;4857:17;;:22;:49;;;;-1:-1:-1;;;;;;4883:18:53;;;:23;4857:49;4853:119;;;4933:24;;-1:-1:-1;;;4933:24:53;;-1:-1:-1;;;;;1095:32:151;;4933:24:53;;;1077:51:151;1050:18;;4933:24:53;;;;;;;4853:119;-1:-1:-1;4992:10:53;4605:408;4437:582;;;;;:::o;5559:487::-;5690:17;;:21;5686:354;;5887:10;5881:17;5943:15;5930:10;5926:2;5922:19;5915:44;5686:354;6010:19;;-1:-1:-1;;;6010:19:53;;;;;;;;;;;5686:354;5559:487;:::o;-1:-1:-1:-;;;;;;;;:::o;:::-;;;;;;;;:::o;14:131:151:-;-1:-1:-1;;;;;89:31:151;;79:42;;69:70;;135:1;132;125:12;150:456;227:6;235;243;296:2;284:9;275:7;271:23;267:32;264:52;;;312:1;309;302:12;264:52;351:9;338:23;370:31;395:5;370:31;:::i;:::-;420:5;-1:-1:-1;477:2:151;462:18;;449:32;490:33;449:32;490:33;:::i;:::-;150:456;;542:7;;-1:-1:-1;;;596:2:151;581:18;;;;568:32;;150:456::o;611:315::-;679:6;687;740:2;728:9;719:7;715:23;711:32;708:52;;;756:1;753;746:12;708:52;795:9;782:23;814:31;839:5;814:31;:::i;:::-;864:5;916:2;901:18;;;;888:32;;-1:-1:-1;;;611:315:151:o;1369:247::-;1428:6;1481:2;1469:9;1460:7;1456:23;1452:32;1449:52;;;1497:1;1494;1487:12;1449:52;1536:9;1523:23;1555:31;1580:5;1555:31;:::i;2101:180::-;2160:6;2213:2;2201:9;2192:7;2188:23;2184:32;2181:52;;;2229:1;2226;2219:12;2181:52;-1:-1:-1;2252:23:151;;2101:180;-1:-1:-1;2101:180:151:o;3136:127::-;3197:10;3192:3;3188:20;3185:1;3178:31;3228:4;3225:1;3218:15;3252:4;3249:1;3242:15;3268:277;3335:6;3388:2;3376:9;3367:7;3363:23;3359:32;3356:52;;;3404:1;3401;3394:12;3356:52;3436:9;3430:16;3489:5;3482:13;3475:21;3468:5;3465:32;3455:60;;3511:1;3508;3501:12;3550:184;3620:6;3673:2;3661:9;3652:7;3648:23;3644:32;3641:52;;;3689:1;3686;3679:12;3641:52;-1:-1:-1;3712:16:151;;3550:184;-1:-1:-1;3550:184:151:o;3739:301::-;3868:3;3906:6;3900:13;3952:6;3945:4;3937:6;3933:17;3928:3;3922:37;4014:1;3978:16;;4003:13;;;-1:-1:-1;3978:16:151;3739:301;-1:-1:-1;3739:301:151:o", "linkReferences": {}, "immutableReferences": {"75176": [{"start": 467, "length": 32}, {"start": 1137, "length": 32}], "75179": [{"start": 702, "length": 32}, {"start": 1170, "length": 32}]}}, "methodIdentifiers": {"WETH()": "ad5c4648", "addLidoDepositHook(address)": "55d0d13d", "addRiskManager(uint256)": "da09bd7e", "addSubvault(address,address,uint256)": "9434438c", "afterDepositHookCall(address,uint256)": "5447e69c", "beforeRedeemHookCall(address,uint256)": "26fc0c2b", "getLiquidAssetsCall(address)": "e717e0c3", "hookPullAssets(address,address,uint256)": "b014f445", "hookPushAssets(address,address,uint256)": "e6c8cd9f", "lidoDepositHook()": "40a119ec", "lidoDepositHookCall(address,uint256)": "e4ec21c0", "pullAssets(address,address,uint256)": "17cbc8b1", "pushAssets(address,address,uint256)": "e984f353", "redeemHook()": "4acbe766", "redirectingDepositHook()": "d977831c", "riskManager()": "47842663", "subvaultAt(uint256)": "9bd0911b", "subvaults()": "a35f620a", "test()": "f8a8fd6d", "wstETH()": "4aa07e64"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"}],\"name\":\"AddressEmptyCode\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"FailedCall\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"WETH\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"nextHook\",\"type\":\"address\"}],\"name\":\"addLidoDepositHook\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"limit\",\"type\":\"uint256\"}],\"name\":\"addRiskManager\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"subvault_\",\"type\":\"address\"},{\"internalType\":\"contract MockERC20\",\"name\":\"asset_\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount_\",\"type\":\"uint256\"}],\"name\":\"addSubvault\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"}],\"name\":\"afterDepositHookCall\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"}],\"name\":\"beforeRedeemHookCall\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"}],\"name\":\"getLiquidAssetsCall\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"subvault_\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"hookPullAssets\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"subvault_\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"hookPushAssets\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"lidoDepositHook\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"}],\"name\":\"lidoDepositHookCall\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"subvault_\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"pullAssets\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"subvault_\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"pushAssets\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"redeemHook\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"redirectingDepositHook\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"riskManager\",\"outputs\":[{\"internalType\":\"contract IRiskManager\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"subvaultAt\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"subvaults\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"wstETH\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"stateMutability\":\"payable\",\"type\":\"receive\"}],\"devdoc\":{\"errors\":{\"AddressEmptyCode(address)\":[{\"details\":\"There's no code at `target` (it is not a contract).\"}],\"FailedCall()\":[{\"details\":\"A call to an address target failed. The target may have reverted.\"}]},\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/mocks/MockVault.sol\":\"MockVault\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/contracts/access/AccessControlUpgradeable.sol\":{\"keccak256\":\"0x0d4d7d19a052a2ef9d17b28450133631188b895e1755747fa8ad0280aadfb534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://838551e10cc07e570b70aee1078e490658d3a0ab8f4adfd4f3f2a565200753da\",\"dweb:/ipfs/QmceT7R3Pqzywyxr3MSEA7VXU4axZAPS6BLy92iV97rGdG\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol\":{\"keccak256\":\"0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6\",\"dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/access/extensions/AccessControlEnumerableUpgradeable.sol\":{\"keccak256\":\"0xaec38804089a16494f7d45ebbbeab78fe05e5dc4bc2eae1d2af458fe999fb43c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://04796408e2c4a1051afb23319e9505edb595a3d9c663ff5d6fcfb8e185ab1d93\",\"dweb:/ipfs/Qma8ZTi4kGyqg45SyNibbJSqMF7broiQnJPHhEGLcZyF9U\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08\",\"dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ReentrancyGuardUpgradeable.sol\":{\"keccak256\":\"0x361126a17677994081cd9cb69c3f50cffff6e920d25cb7e428acdb1ae41d1866\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://19ae787a7dd001269cd60a394b1a5261b78925a0fc3a6f927beb2986a9aa56cf\",\"dweb:/ipfs/QmYLfXiuKmcRgTDBEDXMMjXU8t6JxsspUmjxYzqWS55oEv\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/introspection/ERC165Upgradeable.sol\":{\"keccak256\":\"0xc8ed8d2056934b7675b695dec032f2920c2f5c6cf33a17ca85650940675323ab\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3c8ccc75d1cd792d192aa09e54dd49ea35fe85baa9fcd17486f29227d9f29b89\",\"dweb:/ipfs/QmbboSbFUEiM9tdEgBwuTRb7bykFoJXZ7dsSr1PSREJXMr\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0\",\"dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/extensions/IAccessControlEnumerable.sol\":{\"keccak256\":\"0xca774fbe0568762efdc1a7cba31f09549c7fa96dbe97410f4843fa2f0bc000a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0187ffdbf3d61b6d86cba4fcd9826e53d876987d620533ee84c681bdaf0f3ba3\",\"dweb:/ipfs/QmVJDqdJv6uzHY7ifncfv2QJep8XTzS3bGb4s5Exhuv86m\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0x9b6b3e7803bc5f2f8cd7ad57db8ac1def61a9930a5a3107df4882e028a9605d7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://da62d6be1f5c6edf577f0cb45666a8aa9c2086a4bac87d95d65f02e2f4c36a4b\",\"dweb:/ipfs/QmNkpvBpoCMvX8JwAFNSc5XxJ2q5BXJpL5L1txb4QkqVFF\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0xde7e9fd9aee8d4f40772f96bb3b58836cbc6dfc0227014a061947f8821ea9724\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://11fea9f8bc98949ac6709f0c1699db7430d2948137aa94d5a9e95a91f61a710a\",\"dweb:/ipfs/QmQdfRXxQjwP6yn3DVo1GHPpriKNcFghSPi94Z1oKEFUNS\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol\":{\"keccak256\":\"0xb25a4f11fa80c702bf5cd85adec90e6f6f507f32f4a8e6f5dbc31e8c10029486\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6917f8a323e7811f041aecd4d9fd6e92455a6fba38a797ac6f6e208c7912b79d\",\"dweb:/ipfs/QmShuYv55wYHGi4EFkDB8QfF7ZCHoKk2efyz3AWY1ExSq7\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0xce41876e78d1badc0512229b4d14e4daf83bc1003d7f83978d18e0e56f965b9c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a2608291cb038b388d80b79a06b6118a42f7894ff67b7da10ec0dbbf5b2973ba\",\"dweb:/ipfs/QmWohqcBLbcxmA4eGPhZDXe5RYMMEEpFq22nfkaUMvTfw1\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x880da465c203cec76b10d72dbd87c80f387df4102274f23eea1f9c9b0918792b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://399594cd8bb0143bc9e55e0f1d071d0d8c850a394fb7a319d50edd55d9ed822b\",\"dweb:/ipfs/QmbPZzgtT6LEm9CMqWfagQFwETbV1ztpECBB1DtQHrKiRz\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol\":{\"keccak256\":\"0xa3066ff86b94128a9d3956a63a0511fa1aae41bd455772ab587b32ff322acb2e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bf7b192fd82acf6187970c80548f624b1b9c80425b62fa49e7fdb538a52de049\",\"dweb:/ipfs/QmWXG1YCde1tqDYTbNwjkZDWVgPEjzaQGSDqWkyKLzaNua\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol\":{\"keccak256\":\"0x8decfa54cec979c824b044b8128cd91d713f72c71fd7dfa54974624d8c949898\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://271f914261a19d87117a777e0924ada545c16191ef9b00cc40b0134fc14ebc70\",\"dweb:/ipfs/QmdvVNWHGHQrGGPonZJs5NuzTevTjZRM2zayKrDJf7WBA2\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/Proxy.sol\":{\"keccak256\":\"0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac\",\"dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol\":{\"keccak256\":\"0xc59a78b07b44b2cf2e8ab4175fca91e8eca1eee2df7357b8d2a8833e5ea1f64c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5aa4f07e65444784c29cd7bfcc2341b34381e4e5b5da9f0c5bd00d7f430e66fa\",\"dweb:/ipfs/QmWRMh4Q9DpaU9GvsiXmDdoNYMyyece9if7hnfLz7uqzWM\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol\":{\"keccak256\":\"0x46f86003755f50eff00a7c5aaf493ae62e024142b8aec4493a313851d3c14872\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://462c770cb667cc75ab22df5a29c50873b24b267274edf87ac5cfe0112bf4c3f7\",\"dweb:/ipfs/QmTfKk32AkQ2yyYZCYJ44V69EM5t9ryBFC6bRF7FVMHXvj\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol\":{\"keccak256\":\"0x92579f452fe663595a898cbac85d80bb3868a6c9f034f19ba7fbebdfa3b65a4d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d07a888cd4a26c7077a30dd99e9ac30fbe2752bfb37e58c65f06a3b192079ef3\",\"dweb:/ipfs/QmZCQY4FC4ynBv9dha4BBWM1vYxTUJPBdFBS4HEFe6XzVB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x41f6b3b9e030561e7896dbef372b499cc8d418a80c3884a4d65a68f2fdc7493a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://80b0992a11b2fd1f75ced2971696d07bbd1d19ce6761dd50d8b6d48aa435f42a\",\"dweb:/ipfs/QmZDe5xd2gXHjVEjv9t8C1KQ68K5T8qFwdinwQgmP3rF3x\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xe06a3f08a987af6ad2e1c1e774405d4fe08f1694b67517438b467cecf0da0ef7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://df6f0c459663c9858b6cba2cda1d14a7d05a985bed6d2de72bd8e78c25ee79db\",\"dweb:/ipfs/QmeTTxZ7qVk9rjEv2R4CpCwdf8UMCcRqDNMvzNxHc3Fnn9\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0x70f2f713b13b7ce4610bcd0ac9fec0f3cc43693b043abcb8dc40a42a726eb330\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c13d13304ac79a83ab1c30168967d19e2203342ebbd6a9bbce4db7550522dcbf\",\"dweb:/ipfs/QmeN5jKMN2vw5bhacr6tkg78afbTTZUeaacNHqjWt4Ew1r\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8150ceb4ac947e8a442b2a9c017e01e880b2be2dd958f1fa9bc405f4c5a86508\",\"dweb:/ipfs/QmbcBmFX66AY6Kbhnd5gx7zpkgqnUafo43XnmayAM7zVdB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol\":{\"keccak256\":\"0xb5afb8e8eebc4d1c6404df2f5e1e6d2c3d24fd01e5dfc855314951ecfaae462d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://78586466c424f076c6a2a551d848cfbe3f7c49e723830807598484a1047b3b34\",\"dweb:/ipfs/Qmb717ovcFxm7qgNKEShiV6M9SPR3v1qnNpAGH84D6w29p\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Address.sol\":{\"keccak256\":\"0xaaa1d17c1129b127a4a401db2fbd72960e2671474be3d08cae71ccdc42f7624c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cb2f27cd3952aa667e198fba0d9b7bcec52fbb12c16f013c25fe6fb52b29cc0e\",\"dweb:/ipfs/QmeuohBFoeyDPZA9JNCTEDz3VBfBD4EABWuWXVhHAuEpKR\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol\":{\"keccak256\":\"0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd\",\"dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol\":{\"keccak256\":\"0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd\",\"dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Errors.sol\":{\"keccak256\":\"0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf\",\"dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol\":{\"keccak256\":\"0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2\",\"dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/Hashes.sol\":{\"keccak256\":\"0x8cbd338f083224b4b6f0ff42cbda641a0a6c31ffcdca197452b97fe4d0918269\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f517dec5ba0c6491395acbf7f1d621f4e89e8f218bf5303c867b1c5ad70c6b11\",\"dweb:/ipfs/QmWmXHRLEw8W6ckth7NyYTU88YfvuS7xSsfae5ksL8qNUe\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/MerkleProof.sol\":{\"keccak256\":\"0x36a0c409c437a753cac9b92b75f93b0fbe92803bf2c8ff1517e54b247f166134\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f91ba472de411aa557cdbf6560c40750d87bd11c9060bc04d2ba7119af9d5a6\",\"dweb:/ipfs/QmQjtYo2i7dDvzCEzZ67bDoNSG4RrwMoxPWuqFmX5Xzpuw\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621\",\"dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol\":{\"keccak256\":\"0x93ce66a307d66cb14e95dbc8f20641cedf48b2d060bf3bda431d62a4493e6e8d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://10d4122ee13fd8dcd2f468a86031665345d2dfdda76993d1f51201bdeb746f9d\",\"dweb:/ipfs/QmWxbbYEgyDCjwT4ZtNwujJwB4vdnR2qEKwL2Zrz9JoSDU\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableMap.sol\":{\"keccak256\":\"0x5d0c69ef32eae686acc22acfb1a54df9c91356e69adec593aa69eba83ae8f6f4\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f7a9fca2cb3d802c4f8a2d0fda84ccc7f880264885801c7df08373501dd22282\",\"dweb:/ipfs/QmdtM2Loz9NzNTuQ3RXNQCPBjoPUJw35Ukpt1MSM9XH3cD\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol\":{\"keccak256\":\"0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231\",\"dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y\"]},\"src/hooks/BasicRedeemHook.sol\":{\"keccak256\":\"0xb6a8af98dbc80efe6120a0598ae184404ca4ba3f662ee050e7f921299ee2574d\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://8048a2824817ef50143834b5df3f2ddf066fa7ddc21b38e5713f271d2829deff\",\"dweb:/ipfs/QmXq2UawyZPgRjEcir3tRyH8HCNmA8F8cGrjsnC4NeHLJc\"]},\"src/hooks/LidoDepositHook.sol\":{\"keccak256\":\"0x9018b1143ced609c9e4823eb1ba286f6035ae8af374cd2c78f18ca9903b55f76\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://3364334643db5d1c9c6ec604ef1c43cd3218cb8785bffc0197f0db9d34234e78\",\"dweb:/ipfs/QmWi9ADGArVWvf632vDEVhWqfEopxFZ16DaYKSyFrJb947\"]},\"src/hooks/RedirectingDepositHook.sol\":{\"keccak256\":\"0xd02fd0400ba827319072642816f23f706147408ea8ce806445ce4fe544658c5e\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://9cc2de84cfa5726f1159c099ab58b6ddf1954c7a9720e3461d80028b19e747e6\",\"dweb:/ipfs/QmQTKhkz5UxasyuB67yZj2PDKsB2LutYjPnRqzSRGjQ6q8\"]},\"src/interfaces/external/tokens/IWETH.sol\":{\"keccak256\":\"0x0b7dcdc2713f5f75105a7ed5d6c35341d1d102bdda563717a98db9c329426514\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c87103da00019d8555a04c3325a99eaf2e5e7a321319f23bd4eac92ddd70d18d\",\"dweb:/ipfs/QmSpFTyRndVTZDPVfPpHrWfHQXamc4p2M1dde4J5uAsKZG\"]},\"src/interfaces/external/tokens/IWSTETH.sol\":{\"keccak256\":\"0xd836767c06d507064f4dbfcad077ff827237bed25e0a5a0f11bc8acebe7ec2c6\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://1f9eedf7e5d048fd6cf78275916d9f65b3e4bcb18fc040e69dcf8e9d6c5624c1\",\"dweb:/ipfs/QmRmJGtdyN9n32YkhSLcxthk8hZUWYr1xfB6yHimG45Mzu\"]},\"src/interfaces/factories/IFactory.sol\":{\"keccak256\":\"0x838a308272620b2ae6fdf1f5b425f8ba1439ed24a7baad50599d3fb34eb0bfa7\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7a801db63324ec50da072b69c67ca98eb89d026f01a4aa7e8f60d757bc82f86d\",\"dweb:/ipfs/QmaJ6WHAe8vfwX3A8yHyLW2aJKqkbQCKuthRg1QU2f3TuY\"]},\"src/interfaces/factories/IFactoryEntity.sol\":{\"keccak256\":\"0xa09fc818b5484f21f20f46535537d82a8df88379bdb4283887010f5dcf3b7bf5\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://48202a8213b1c37f6f17f8b85eee5006655746f7cdca32d69616ae9cc1765812\",\"dweb:/ipfs/QmT1RNDTou59wmw7ByeKiGf6gr9kS3mZwom6its5pkeehx\"]},\"src/interfaces/hooks/IHook.sol\":{\"keccak256\":\"0xf414604a4cbf1feb04f976a806eb2825fa7dcce6815e209fc7aa50c6fe845bb3\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://a727a899804756179d294bfb0975487e8c6ca11f68aa741604d400ec6dfbf241\",\"dweb:/ipfs/QmRqsAigFe71aHAG5SgyKQKVpnHoiVyiP6VmnkxFK9KRz9\"]},\"src/interfaces/hooks/IRedeemHook.sol\":{\"keccak256\":\"0xd3df4e8013847ea38f603529d1062e6fccfc757ea62339bfbdd72719d2f7fbb1\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cddd2d10f020fa96f03b728dfcf8c7874942a19d871414010437ca3cdaef2974\",\"dweb:/ipfs/QmUZwodA14vQg3ntpzF6YxVQ2JCDmjXgQKrzE6HzJ19Mq8\"]},\"src/interfaces/managers/IFeeManager.sol\":{\"keccak256\":\"0x37d3c06ed6b23e367216c2dfc14f99f3fd6d686c984eca124406c2002e4f1516\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://5b3b1b974856f76c6fe2347895ab05e973c84c963fdf8f5483c37e2e4d434cad\",\"dweb:/ipfs/QmVeLCrCWgeQ3aS4V38y9FszFvZdSkbPHKKCMWKKM3V4qw\"]},\"src/interfaces/managers/IRiskManager.sol\":{\"keccak256\":\"0xb0dda9606a25e8187fb20cff7eb99dfecde18fbea30bae52a2124fd22afda97a\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://fee1bae75e2154b21e5ea2382ff21cd3e2ff2ea3a5e67828cfe4b563ee86e14e\",\"dweb:/ipfs/QmUtFozDVfCzehRMYxhJwZAbLJ43afzzVQDA89JD1HG1YH\"]},\"src/interfaces/managers/IShareManager.sol\":{\"keccak256\":\"0xb75fcb890e9a3e706c0d87781700965aa24faa8461967ee62b140d45f4c399e8\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ae50d1dda35eb9f6fa035fc39c2424f2e2eb2d41e6b0ffed296719818b1d9502\",\"dweb:/ipfs/Qme5jPDZG3WTLYtT2s8VFs3MKJVXgL9T5AdAzoXuuqM79z\"]},\"src/interfaces/modules/IACLModule.sol\":{\"keccak256\":\"0xf6ce9b4f4af5475d152845d6afa6f57b2d376c3a6d3eec2b5ac9650901a78243\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://f851cef38b6188b20be98c95047c8bd9d8651d37421a54e1d02b60f9f372c5ed\",\"dweb:/ipfs/QmaymGDd2RoKweuASXCiV3rPuhwvLXUvb6wBgi8xXoiYT8\"]},\"src/interfaces/modules/IBaseModule.sol\":{\"keccak256\":\"0x9c5c95ceb39f4c39f81f2afc7ddb55389a21315e76047a9cf66521db4dea68c3\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ccfb0453b14b105cb5990978a49c1e08f812563a046e17c354f4f1c648f95fe7\",\"dweb:/ipfs/QmYMAKaVdRq3NoTMfqadxgMMmUq6ejzTw2Lpsa3e9a3eyj\"]},\"src/interfaces/modules/IShareModule.sol\":{\"keccak256\":\"0x65ef35a38441080ad4e0c77c7668d6717fe9699df679ed4d2c775f47d1dce57a\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://e28e0e9e2dd2097df748fa2015ce4582f489615040b5323094880a618c54edac\",\"dweb:/ipfs/QmXNvxJDQ6edM3ExmmiTEmS5UreWYTDBgaAygU2kM6mgC6\"]},\"src/interfaces/modules/ISubvaultModule.sol\":{\"keccak256\":\"0x8db6f1a3bd2506dd0db82f4138f0436602ed37487717095a804dee81e44f5e0e\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://2547472f59c19612916f3f54e6fa69644ec31f14c669aa66dea3c006bc71bccc\",\"dweb:/ipfs/QmZ9VRCsofVntdB7siM3pfypUpgJnmaJAC1n8HviQb3K3U\"]},\"src/interfaces/modules/IVaultModule.sol\":{\"keccak256\":\"0x7696792b852bc9b435e6388a19f4284854259df2f50b45e2b833b6f9dd1b1648\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://4364151c043fc5e56bc87c2cdbffc169b80cd745618a4b0da14e271752de6637\",\"dweb:/ipfs/Qmdfx3L8goy8eWFGimtsh58Se1vuLsukUw2mXo5chCbTy2\"]},\"src/interfaces/modules/IVerifierModule.sol\":{\"keccak256\":\"0x8b8ad9ea43b139afccc77ceb0fdcc34615c7401cc13e99c723895302049065a5\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://0d578b1a55a180c652ee24e1b35f6d70d65bc067770dd791db44d0bcfa7a5d38\",\"dweb:/ipfs/QmexSPCfixS9spdjCydR4FUaoN4J188sfLkRmJaia14yk6\"]},\"src/interfaces/oracles/IOracle.sol\":{\"keccak256\":\"0xcbf84560c1fb9f7d3445cbbca4f5f3406606496f30867222d8e3deabad741296\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c90107edcd30e29044a5a8b05e71e728b93a485e7e7ef1a5b4ff695430e33a5e\",\"dweb:/ipfs/QmfHGNxtRQWeq7JENynxZNfhfTBytwbRwyekUyuHWrUyjK\"]},\"src/interfaces/permissions/ICustomVerifier.sol\":{\"keccak256\":\"0xdb343366ed86f63f8a94ff478c5c8e1d127888e4993c27fcb3bb3b51ae0cb8c7\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://12407e89b20982a00961ccc03258e353962607747f214940d18b0989c11547c9\",\"dweb:/ipfs/Qmay19iWpwiV8LuvWccPjsazVU5Kkhme9QykFfLKfBmvSr\"]},\"src/interfaces/permissions/IMellowACL.sol\":{\"keccak256\":\"0xcc86b0ce4e4a53c1c4fa16ad79f9de0e2a1f1a77a1f256c0856120a9d6ea36df\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://6942df2ffadacae5563e01ec60697f16116362ba84a856a346b36bc7b9dd1ec2\",\"dweb:/ipfs/QmNYhqZGpS98UP89CS86CjoG4ZWbMHzTHRtd7bZzQqn3UY\"]},\"src/interfaces/permissions/IVerifier.sol\":{\"keccak256\":\"0x4b42731d76efaffcb017577bd2a478d00a3058c69220d6ea8e26f30f74476be7\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ff2a6f09a946dd8a470dfe495b2a64d24b849c6d071dc859589140fb1b983362\",\"dweb:/ipfs/Qmb6UoqkmaC8tJMqkMfjcWR7pn1WCwPwuKKvUbLBQbPcJS\"]},\"src/interfaces/queues/IDepositQueue.sol\":{\"keccak256\":\"0x4ef38cb59d419010f453e62fe101efec25bec85a2ce1a24048098034060db47b\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://20e51dd02001309f23f46abea409edf77a39522bf21eeffb858ac888e4200e72\",\"dweb:/ipfs/QmWm9gfMuXz8nwuHb3he7abxTRgQw35x2viwMVuiNYAY5b\"]},\"src/interfaces/queues/IQueue.sol\":{\"keccak256\":\"0xd12e34631b204821c77eaa5b2e104902d740e3530cd716f7fc991af01a73b9ab\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://1f2a4d664244be639da1a5025ebccc8c1127e087a81ce6d0fc76af5134fec311\",\"dweb:/ipfs/QmS1GJwxGCV89myCAzLBAbmSrY6YL31G3nGj542dDM4QmT\"]},\"src/interfaces/queues/IRedeemQueue.sol\":{\"keccak256\":\"0xf8f95b95de259c8ccf29976f34d97a0ef31c9827f41ebcd5e8de54c10e646076\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://45ae0d496e865d846bf0c980593c1d16c038abf2b9359906224aa94cccf1c73f\",\"dweb:/ipfs/QmYTDXSTpUqhzJDXSKjoSKxwwQYee7wSTtf617wDsnnCC5\"]},\"src/libraries/FenwickTreeLibrary.sol\":{\"keccak256\":\"0xfd159ed27b96f288c4220febac4bfe286e4545b8ca7f523ae6e63d123341f8a3\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://58f0cc202896722786140571b111bf5e589730358a24b2940a450c4c75d864fe\",\"dweb:/ipfs/QmeRSjvcYURUSpvFe4r4VRwwpHcb1NmqiEUDEJ5rBxAxuf\"]},\"src/libraries/TransferLibrary.sol\":{\"keccak256\":\"0x3f2885a3bc24b3ab880a408f94511475136a8449a49c8c3fb5587de555a6c47e\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://6626b7d9956a63be7ce24825fc22c1f7f45503fa9b2e2ccdf5160b1e026eaa02\",\"dweb:/ipfs/QmX5VrYDquCHKSZMWEw6YNQM2inTiQzLNKM3sTevLgPxzM\"]},\"test/mocks/MockERC20.sol\":{\"keccak256\":\"0xabfdbd8413b69ae05b4d55138e7ee90c0d8706386265eaa84496340f381b8500\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://a638bad1dba281d001763f9ac04a9fd789c923325da7829703d4355aa6b15875\",\"dweb:/ipfs/QmP2EBdnowJPCYVggrBGUZ2kuwoziEDLpcS7U9nmFN6xwc\"]},\"test/mocks/MockRiskManager.sol\":{\"keccak256\":\"0x6f8cbdaff1f0436750b2a61cf7def9d279416c778c57e21f90e500e9b99da02a\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://9dbc697b4328da1f086bb90fe643a685f55d076727589df2509a883e4668876d\",\"dweb:/ipfs/QmdK5nkndDqnwb6yoPd1WkhpNs4R1gSrXQpksVPBXbGbe5\"]},\"test/mocks/MockVault.sol\":{\"keccak256\":\"0x327ec95cd13ea081a9173536f5089054930f007988aece68b3bfeb10e6f3badf\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://34f731b9c01dc8b118a3a21724c6c0c74a0fd19eb926c8665cd35a5802b00cc2\",\"dweb:/ipfs/QmR5rv86qKYWVQcxU3oveeXWZVq5wbN1dWY59h2RoNHtyD\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "type": "error", "name": "AddressEmptyCode"}, {"inputs": [], "type": "error", "name": "FailedCall"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "WETH", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "nextHook", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "addLidoDepositHook"}, {"inputs": [{"internalType": "uint256", "name": "limit", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "addRiskManager"}, {"inputs": [{"internalType": "address", "name": "subvault_", "type": "address"}, {"internalType": "contract MockERC20", "name": "asset_", "type": "address"}, {"internalType": "uint256", "name": "amount_", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "addSubvault"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "assets", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "afterDepositHookCall"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "assets", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "beforeRedeemHookCall"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getLiquidAssetsCall", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "subvault_", "type": "address"}, {"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "hookPullAssets"}, {"inputs": [{"internalType": "address", "name": "subvault_", "type": "address"}, {"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "hookPushAssets"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "lidoDepositHook", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "assets", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "lidoDepositHookCall"}, {"inputs": [{"internalType": "address", "name": "subvault_", "type": "address"}, {"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "pullAssets"}, {"inputs": [{"internalType": "address", "name": "subvault_", "type": "address"}, {"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "pushAssets"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "redeemH<PERSON>", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "redirectingDepositHook", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "riskManager", "outputs": [{"internalType": "contract IRiskManager", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "subvaultAt", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "subvaults", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "wstETH", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "payable", "type": "receive"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/mocks/MockVault.sol": "<PERSON><PERSON><PERSON><PERSON>"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/contracts/access/AccessControlUpgradeable.sol": {"keccak256": "0x0d4d7d19a052a2ef9d17b28450133631188b895e1755747fa8ad0280aadfb534", "urls": ["bzz-raw://838551e10cc07e570b70aee1078e490658d3a0ab8f4adfd4f3f2a565200753da", "dweb:/ipfs/QmceT7R3Pqzywyxr3MSEA7VXU4axZAPS6BLy92iV97rGdG"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol": {"keccak256": "0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a", "urls": ["bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6", "dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/access/extensions/AccessControlEnumerableUpgradeable.sol": {"keccak256": "0xaec38804089a16494f7d45ebbbeab78fe05e5dc4bc2eae1d2af458fe999fb43c", "urls": ["bzz-raw://04796408e2c4a1051afb23319e9505edb595a3d9c663ff5d6fcfb8e185ab1d93", "dweb:/ipfs/Qma8ZTi4kGyqg45SyNibbJSqMF7broiQnJPHhEGLcZyF9U"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05", "urls": ["bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08", "dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ReentrancyGuardUpgradeable.sol": {"keccak256": "0x361126a17677994081cd9cb69c3f50cffff6e920d25cb7e428acdb1ae41d1866", "urls": ["bzz-raw://19ae787a7dd001269cd60a394b1a5261b78925a0fc3a6f927beb2986a9aa56cf", "dweb:/ipfs/QmYLfXiuKmcRgTDBEDXMMjXU8t6JxsspUmjxYzqWS55oEv"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/introspection/ERC165Upgradeable.sol": {"keccak256": "0xc8ed8d2056934b7675b695dec032f2920c2f5c6cf33a17ca85650940675323ab", "urls": ["bzz-raw://3c8ccc75d1cd792d192aa09e54dd49ea35fe85baa9fcd17486f29227d9f29b89", "dweb:/ipfs/QmbboSbFUEiM9tdEgBwuTRb7bykFoJXZ7dsSr1PSREJXMr"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/IAccessControl.sol": {"keccak256": "0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530", "urls": ["bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0", "dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/extensions/IAccessControlEnumerable.sol": {"keccak256": "0xca774fbe0568762efdc1a7cba31f09549c7fa96dbe97410f4843fa2f0bc000a3", "urls": ["bzz-raw://0187ffdbf3d61b6d86cba4fcd9826e53d876987d620533ee84c681bdaf0f3ba3", "dweb:/ipfs/QmVJDqdJv6uzHY7ifncfv2QJep8XTzS3bGb4s5Exhuv86m"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol": {"keccak256": "0x9b6b3e7803bc5f2f8cd7ad57db8ac1def61a9930a5a3107df4882e028a9605d7", "urls": ["bzz-raw://da62d6be1f5c6edf577f0cb45666a8aa9c2086a4bac87d95d65f02e2f4c36a4b", "dweb:/ipfs/QmNkpvBpoCMvX8JwAFNSc5XxJ2q5BXJpL5L1txb4QkqVFF"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol": {"keccak256": "0xde7e9fd9aee8d4f40772f96bb3b58836cbc6dfc0227014a061947f8821ea9724", "urls": ["bzz-raw://11fea9f8bc98949ac6709f0c1699db7430d2948137aa94d5a9e95a91f61a710a", "dweb:/ipfs/QmQdfRXxQjwP6yn3DVo1GHPpriKNcFghSPi94Z1oKEFUNS"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol": {"keccak256": "0xb25a4f11fa80c702bf5cd85adec90e6f6f507f32f4a8e6f5dbc31e8c10029486", "urls": ["bzz-raw://6917f8a323e7811f041aecd4d9fd6e92455a6fba38a797ac6f6e208c7912b79d", "dweb:/ipfs/QmShuYv55wYHGi4EFkDB8QfF7ZCHoKk2efyz3AWY1ExSq7"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol": {"keccak256": "0xce41876e78d1badc0512229b4d14e4daf83bc1003d7f83978d18e0e56f965b9c", "urls": ["bzz-raw://a2608291cb038b388d80b79a06b6118a42f7894ff67b7da10ec0dbbf5b2973ba", "dweb:/ipfs/QmWohqcBLbcxmA4eGPhZDXe5RYMMEEpFq22nfkaUMvTfw1"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x880da465c203cec76b10d72dbd87c80f387df4102274f23eea1f9c9b0918792b", "urls": ["bzz-raw://399594cd8bb0143bc9e55e0f1d071d0d8c850a394fb7a319d50edd55d9ed822b", "dweb:/ipfs/QmbPZzgtT6LEm9CMqWfagQFwETbV1ztpECBB1DtQHrKiRz"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol": {"keccak256": "0xa3066ff86b94128a9d3956a63a0511fa1aae41bd455772ab587b32ff322acb2e", "urls": ["bzz-raw://bf7b192fd82acf6187970c80548f624b1b9c80425b62fa49e7fdb538a52de049", "dweb:/ipfs/QmWXG1YCde1tqDYTbNwjkZDWVgPEjzaQGSDqWkyKLzaNua"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol": {"keccak256": "0x8decfa54cec979c824b044b8128cd91d713f72c71fd7dfa54974624d8c949898", "urls": ["bzz-raw://271f914261a19d87117a777e0924ada545c16191ef9b00cc40b0134fc14ebc70", "dweb:/ipfs/QmdvVNWHGHQrGGPonZJs5NuzTevTjZRM2zayKrDJf7WBA2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/Proxy.sol": {"keccak256": "0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd", "urls": ["bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac", "dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol": {"keccak256": "0xc59a78b07b44b2cf2e8ab4175fca91e8eca1eee2df7357b8d2a8833e5ea1f64c", "urls": ["bzz-raw://5aa4f07e65444784c29cd7bfcc2341b34381e4e5b5da9f0c5bd00d7f430e66fa", "dweb:/ipfs/QmWRMh4Q9DpaU9GvsiXmDdoNYMyyece9if7hnfLz7uqzWM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol": {"keccak256": "0x46f86003755f50eff00a7c5aaf493ae62e024142b8aec4493a313851d3c14872", "urls": ["bzz-raw://462c770cb667cc75ab22df5a29c50873b24b267274edf87ac5cfe0112bf4c3f7", "dweb:/ipfs/QmTfKk32AkQ2yyYZCYJ44V69EM5t9ryBFC6bRF7FVMHXvj"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol": {"keccak256": "0x92579f452fe663595a898cbac85d80bb3868a6c9f034f19ba7fbebdfa3b65a4d", "urls": ["bzz-raw://d07a888cd4a26c7077a30dd99e9ac30fbe2752bfb37e58c65f06a3b192079ef3", "dweb:/ipfs/QmZCQY4FC4ynBv9dha4BBWM1vYxTUJPBdFBS4HEFe6XzVB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x41f6b3b9e030561e7896dbef372b499cc8d418a80c3884a4d65a68f2fdc7493a", "urls": ["bzz-raw://80b0992a11b2fd1f75ced2971696d07bbd1d19ce6761dd50d8b6d48aa435f42a", "dweb:/ipfs/QmZDe5xd2gXHjVEjv9t8C1KQ68K5T8qFwdinwQgmP3rF3x"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xe06a3f08a987af6ad2e1c1e774405d4fe08f1694b67517438b467cecf0da0ef7", "urls": ["bzz-raw://df6f0c459663c9858b6cba2cda1d14a7d05a985bed6d2de72bd8e78c25ee79db", "dweb:/ipfs/QmeTTxZ7qVk9rjEv2R4CpCwdf8UMCcRqDNMvzNxHc3Fnn9"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0x70f2f713b13b7ce4610bcd0ac9fec0f3cc43693b043abcb8dc40a42a726eb330", "urls": ["bzz-raw://c13d13304ac79a83ab1c30168967d19e2203342ebbd6a9bbce4db7550522dcbf", "dweb:/ipfs/QmeN5jKMN2vw5bhacr6tkg78afbTTZUeaacNHqjWt4Ew1r"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5", "urls": ["bzz-raw://8150ceb4ac947e8a442b2a9c017e01e880b2be2dd958f1fa9bc405f4c5a86508", "dweb:/ipfs/QmbcBmFX66AY6Kbhnd5gx7zpkgqnUafo43XnmayAM7zVdB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol": {"keccak256": "0xb5afb8e8eebc4d1c6404df2f5e1e6d2c3d24fd01e5dfc855314951ecfaae462d", "urls": ["bzz-raw://78586466c424f076c6a2a551d848cfbe3f7c49e723830807598484a1047b3b34", "dweb:/ipfs/Qmb717ovcFxm7qgNKEShiV6M9SPR3v1qnNpAGH84D6w29p"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Address.sol": {"keccak256": "0xaaa1d17c1129b127a4a401db2fbd72960e2671474be3d08cae71ccdc42f7624c", "urls": ["bzz-raw://cb2f27cd3952aa667e198fba0d9b7bcec52fbb12c16f013c25fe6fb52b29cc0e", "dweb:/ipfs/QmeuohBFoeyDPZA9JNCTEDz3VBfBD4EABWuWXVhHAuEpKR"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol": {"keccak256": "0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6", "urls": ["bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd", "dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol": {"keccak256": "0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58", "urls": ["bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd", "dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Errors.sol": {"keccak256": "0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123", "urls": ["bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf", "dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol": {"keccak256": "0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26", "urls": ["bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2", "dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/Hashes.sol": {"keccak256": "0x8cbd338f083224b4b6f0ff42cbda641a0a6c31ffcdca197452b97fe4d0918269", "urls": ["bzz-raw://f517dec5ba0c6491395acbf7f1d621f4e89e8f218bf5303c867b1c5ad70c6b11", "dweb:/ipfs/QmWmXHRLEw8W6ckth7NyYTU88YfvuS7xSsfae5ksL8qNUe"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/MerkleProof.sol": {"keccak256": "0x36a0c409c437a753cac9b92b75f93b0fbe92803bf2c8ff1517e54b247f166134", "urls": ["bzz-raw://0f91ba472de411aa557cdbf6560c40750d87bd11c9060bc04d2ba7119af9d5a6", "dweb:/ipfs/QmQjtYo2i7dDvzCEzZ67bDoNSG4RrwMoxPWuqFmX5Xzpuw"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8", "urls": ["bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621", "dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol": {"keccak256": "0x93ce66a307d66cb14e95dbc8f20641cedf48b2d060bf3bda431d62a4493e6e8d", "urls": ["bzz-raw://10d4122ee13fd8dcd2f468a86031665345d2dfdda76993d1f51201bdeb746f9d", "dweb:/ipfs/QmWxbbYEgyDCjwT4ZtNwujJwB4vdnR2qEKwL2Zrz9JoSDU"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableMap.sol": {"keccak256": "0x5d0c69ef32eae686acc22acfb1a54df9c91356e69adec593aa69eba83ae8f6f4", "urls": ["bzz-raw://f7a9fca2cb3d802c4f8a2d0fda84ccc7f880264885801c7df08373501dd22282", "dweb:/ipfs/QmdtM2Loz9NzNTuQ3RXNQCPBjoPUJw35Ukpt1MSM9XH3cD"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol": {"keccak256": "0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06", "urls": ["bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231", "dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y"], "license": "MIT"}, "src/hooks/BasicRedeemHook.sol": {"keccak256": "0xb6a8af98dbc80efe6120a0598ae184404ca4ba3f662ee050e7f921299ee2574d", "urls": ["bzz-raw://8048a2824817ef50143834b5df3f2ddf066fa7ddc21b38e5713f271d2829deff", "dweb:/ipfs/QmXq2UawyZPgRjEcir3tRyH8HCNmA8F8cGrjsnC4NeHLJc"], "license": "BUSL-1.1"}, "src/hooks/LidoDepositHook.sol": {"keccak256": "0x9018b1143ced609c9e4823eb1ba286f6035ae8af374cd2c78f18ca9903b55f76", "urls": ["bzz-raw://3364334643db5d1c9c6ec604ef1c43cd3218cb8785bffc0197f0db9d34234e78", "dweb:/ipfs/QmWi9ADGArVWvf632vDEVhWqfEopxFZ16DaYKSyFrJb947"], "license": "BUSL-1.1"}, "src/hooks/RedirectingDepositHook.sol": {"keccak256": "0xd02fd0400ba827319072642816f23f706147408ea8ce806445ce4fe544658c5e", "urls": ["bzz-raw://9cc2de84cfa5726f1159c099ab58b6ddf1954c7a9720e3461d80028b19e747e6", "dweb:/ipfs/QmQTKhkz5UxasyuB67yZj2PDKsB2LutYjPnRqzSRGjQ6q8"], "license": "BUSL-1.1"}, "src/interfaces/external/tokens/IWETH.sol": {"keccak256": "0x0b7dcdc2713f5f75105a7ed5d6c35341d1d102bdda563717a98db9c329426514", "urls": ["bzz-raw://c87103da00019d8555a04c3325a99eaf2e5e7a321319f23bd4eac92ddd70d18d", "dweb:/ipfs/QmSpFTyRndVTZDPVfPpHrWfHQXamc4p2M1dde4J5uAsKZG"], "license": "BUSL-1.1"}, "src/interfaces/external/tokens/IWSTETH.sol": {"keccak256": "0xd836767c06d507064f4dbfcad077ff827237bed25e0a5a0f11bc8acebe7ec2c6", "urls": ["bzz-raw://1f9eedf7e5d048fd6cf78275916d9f65b3e4bcb18fc040e69dcf8e9d6c5624c1", "dweb:/ipfs/QmRmJGtdyN9n32YkhSLcxthk8hZUWYr1xfB6yHimG45Mzu"], "license": "BUSL-1.1"}, "src/interfaces/factories/IFactory.sol": {"keccak256": "0x838a308272620b2ae6fdf1f5b425f8ba1439ed24a7baad50599d3fb34eb0bfa7", "urls": ["bzz-raw://7a801db63324ec50da072b69c67ca98eb89d026f01a4aa7e8f60d757bc82f86d", "dweb:/ipfs/QmaJ6WHAe8vfwX3A8yHyLW2aJKqkbQCKuthRg1QU2f3TuY"], "license": "BUSL-1.1"}, "src/interfaces/factories/IFactoryEntity.sol": {"keccak256": "0xa09fc818b5484f21f20f46535537d82a8df88379bdb4283887010f5dcf3b7bf5", "urls": ["bzz-raw://48202a8213b1c37f6f17f8b85eee5006655746f7cdca32d69616ae9cc1765812", "dweb:/ipfs/QmT1RNDTou59wmw7ByeKiGf6gr9kS3mZwom6its5pkeehx"], "license": "BUSL-1.1"}, "src/interfaces/hooks/IHook.sol": {"keccak256": "0xf414604a4cbf1feb04f976a806eb2825fa7dcce6815e209fc7aa50c6fe845bb3", "urls": ["bzz-raw://a727a899804756179d294bfb0975487e8c6ca11f68aa741604d400ec6dfbf241", "dweb:/ipfs/QmRqsAigFe71aHAG5SgyKQKVpnHoiVyiP6VmnkxFK9KRz9"], "license": "BUSL-1.1"}, "src/interfaces/hooks/IRedeemHook.sol": {"keccak256": "0xd3df4e8013847ea38f603529d1062e6fccfc757ea62339bfbdd72719d2f7fbb1", "urls": ["bzz-raw://cddd2d10f020fa96f03b728dfcf8c7874942a19d871414010437ca3cdaef2974", "dweb:/ipfs/QmUZwodA14vQg3ntpzF6YxVQ2JCDmjXgQKrzE6HzJ19Mq8"], "license": "BUSL-1.1"}, "src/interfaces/managers/IFeeManager.sol": {"keccak256": "0x37d3c06ed6b23e367216c2dfc14f99f3fd6d686c984eca124406c2002e4f1516", "urls": ["bzz-raw://5b3b1b974856f76c6fe2347895ab05e973c84c963fdf8f5483c37e2e4d434cad", "dweb:/ipfs/QmVeLCrCWgeQ3aS4V38y9FszFvZdSkbPHKKCMWKKM3V4qw"], "license": "BUSL-1.1"}, "src/interfaces/managers/IRiskManager.sol": {"keccak256": "0xb0dda9606a25e8187fb20cff7eb99dfecde18fbea30bae52a2124fd22afda97a", "urls": ["bzz-raw://fee1bae75e2154b21e5ea2382ff21cd3e2ff2ea3a5e67828cfe4b563ee86e14e", "dweb:/ipfs/QmUtFozDVfCzehRMYxhJwZAbLJ43afzzVQDA89JD1HG1YH"], "license": "BUSL-1.1"}, "src/interfaces/managers/IShareManager.sol": {"keccak256": "0xb75fcb890e9a3e706c0d87781700965aa24faa8461967ee62b140d45f4c399e8", "urls": ["bzz-raw://ae50d1dda35eb9f6fa035fc39c2424f2e2eb2d41e6b0ffed296719818b1d9502", "dweb:/ipfs/Qme5jPDZG3WTLYtT2s8VFs3MKJVXgL9T5AdAzoXuuqM79z"], "license": "BUSL-1.1"}, "src/interfaces/modules/IACLModule.sol": {"keccak256": "0xf6ce9b4f4af5475d152845d6afa6f57b2d376c3a6d3eec2b5ac9650901a78243", "urls": ["bzz-raw://f851cef38b6188b20be98c95047c8bd9d8651d37421a54e1d02b60f9f372c5ed", "dweb:/ipfs/QmaymGDd2RoKweuASXCiV3rPuhwvLXUvb6wBgi8xXoiYT8"], "license": "BUSL-1.1"}, "src/interfaces/modules/IBaseModule.sol": {"keccak256": "0x9c5c95ceb39f4c39f81f2afc7ddb55389a21315e76047a9cf66521db4dea68c3", "urls": ["bzz-raw://ccfb0453b14b105cb5990978a49c1e08f812563a046e17c354f4f1c648f95fe7", "dweb:/ipfs/QmYMAKaVdRq3NoTMfqadxgMMmUq6ejzTw2Lpsa3e9a3eyj"], "license": "BUSL-1.1"}, "src/interfaces/modules/IShareModule.sol": {"keccak256": "0x65ef35a38441080ad4e0c77c7668d6717fe9699df679ed4d2c775f47d1dce57a", "urls": ["bzz-raw://e28e0e9e2dd2097df748fa2015ce4582f489615040b5323094880a618c54edac", "dweb:/ipfs/QmXNvxJDQ6edM3ExmmiTEmS5UreWYTDBgaAygU2kM6mgC6"], "license": "BUSL-1.1"}, "src/interfaces/modules/ISubvaultModule.sol": {"keccak256": "0x8db6f1a3bd2506dd0db82f4138f0436602ed37487717095a804dee81e44f5e0e", "urls": ["bzz-raw://2547472f59c19612916f3f54e6fa69644ec31f14c669aa66dea3c006bc71bccc", "dweb:/ipfs/QmZ9VRCsofVntdB7siM3pfypUpgJnmaJAC1n8HviQb3K3U"], "license": "BUSL-1.1"}, "src/interfaces/modules/IVaultModule.sol": {"keccak256": "0x7696792b852bc9b435e6388a19f4284854259df2f50b45e2b833b6f9dd1b1648", "urls": ["bzz-raw://4364151c043fc5e56bc87c2cdbffc169b80cd745618a4b0da14e271752de6637", "dweb:/ipfs/Qmdfx3L8goy8eWFGimtsh58Se1vuLsukUw2mXo5chCbTy2"], "license": "BUSL-1.1"}, "src/interfaces/modules/IVerifierModule.sol": {"keccak256": "0x8b8ad9ea43b139afccc77ceb0fdcc34615c7401cc13e99c723895302049065a5", "urls": ["bzz-raw://0d578b1a55a180c652ee24e1b35f6d70d65bc067770dd791db44d0bcfa7a5d38", "dweb:/ipfs/QmexSPCfixS9spdjCydR4FUaoN4J188sfLkRmJaia14yk6"], "license": "BUSL-1.1"}, "src/interfaces/oracles/IOracle.sol": {"keccak256": "0xcbf84560c1fb9f7d3445cbbca4f5f3406606496f30867222d8e3deabad741296", "urls": ["bzz-raw://c90107edcd30e29044a5a8b05e71e728b93a485e7e7ef1a5b4ff695430e33a5e", "dweb:/ipfs/QmfHGNxtRQWeq7JENynxZNfhfTBytwbRwyekUyuHWrUyjK"], "license": "BUSL-1.1"}, "src/interfaces/permissions/ICustomVerifier.sol": {"keccak256": "0xdb343366ed86f63f8a94ff478c5c8e1d127888e4993c27fcb3bb3b51ae0cb8c7", "urls": ["bzz-raw://12407e89b20982a00961ccc03258e353962607747f214940d18b0989c11547c9", "dweb:/ipfs/Qmay19iWpwiV8LuvWccPjsazVU5Kkhme9QykFfLKfBmvSr"], "license": "BUSL-1.1"}, "src/interfaces/permissions/IMellowACL.sol": {"keccak256": "0xcc86b0ce4e4a53c1c4fa16ad79f9de0e2a1f1a77a1f256c0856120a9d6ea36df", "urls": ["bzz-raw://6942df2ffadacae5563e01ec60697f16116362ba84a856a346b36bc7b9dd1ec2", "dweb:/ipfs/QmNYhqZGpS98UP89CS86CjoG4ZWbMHzTHRtd7bZzQqn3UY"], "license": "BUSL-1.1"}, "src/interfaces/permissions/IVerifier.sol": {"keccak256": "0x4b42731d76efaffcb017577bd2a478d00a3058c69220d6ea8e26f30f74476be7", "urls": ["bzz-raw://ff2a6f09a946dd8a470dfe495b2a64d24b849c6d071dc859589140fb1b983362", "dweb:/ipfs/Qmb6UoqkmaC8tJMqkMfjcWR7pn1WCwPwuKKvUbLBQbPcJS"], "license": "BUSL-1.1"}, "src/interfaces/queues/IDepositQueue.sol": {"keccak256": "0x4ef38cb59d419010f453e62fe101efec25bec85a2ce1a24048098034060db47b", "urls": ["bzz-raw://20e51dd02001309f23f46abea409edf77a39522bf21eeffb858ac888e4200e72", "dweb:/ipfs/QmWm9gfMuXz8nwuHb3he7abxTRgQw35x2viwMVuiNYAY5b"], "license": "BUSL-1.1"}, "src/interfaces/queues/IQueue.sol": {"keccak256": "0xd12e34631b204821c77eaa5b2e104902d740e3530cd716f7fc991af01a73b9ab", "urls": ["bzz-raw://1f2a4d664244be639da1a5025ebccc8c1127e087a81ce6d0fc76af5134fec311", "dweb:/ipfs/QmS1GJwxGCV89myCAzLBAbmSrY6YL31G3nGj542dDM4QmT"], "license": "BUSL-1.1"}, "src/interfaces/queues/IRedeemQueue.sol": {"keccak256": "0xf8f95b95de259c8ccf29976f34d97a0ef31c9827f41ebcd5e8de54c10e646076", "urls": ["bzz-raw://45ae0d496e865d846bf0c980593c1d16c038abf2b9359906224aa94cccf1c73f", "dweb:/ipfs/QmYTDXSTpUqhzJDXSKjoSKxwwQYee7wSTtf617wDsnnCC5"], "license": "BUSL-1.1"}, "src/libraries/FenwickTreeLibrary.sol": {"keccak256": "0xfd159ed27b96f288c4220febac4bfe286e4545b8ca7f523ae6e63d123341f8a3", "urls": ["bzz-raw://58f0cc202896722786140571b111bf5e589730358a24b2940a450c4c75d864fe", "dweb:/ipfs/QmeRSjvcYURUSpvFe4r4VRwwpHcb1NmqiEUDEJ5rBxAxuf"], "license": "BUSL-1.1"}, "src/libraries/TransferLibrary.sol": {"keccak256": "0x3f2885a3bc24b3ab880a408f94511475136a8449a49c8c3fb5587de555a6c47e", "urls": ["bzz-raw://6626b7d9956a63be7ce24825fc22c1f7f45503fa9b2e2ccdf5160b1e026eaa02", "dweb:/ipfs/QmX5VrYDquCHKSZMWEw6YNQM2inTiQzLNKM3sTevLgPxzM"], "license": "BUSL-1.1"}, "test/mocks/MockERC20.sol": {"keccak256": "0xabfdbd8413b69ae05b4d55138e7ee90c0d8706386265eaa84496340f381b8500", "urls": ["bzz-raw://a638bad1dba281d001763f9ac04a9fd789c923325da7829703d4355aa6b15875", "dweb:/ipfs/QmP2EBdnowJPCYVggrBGUZ2kuwoziEDLpcS7U9nmFN6xwc"], "license": "BUSL-1.1"}, "test/mocks/MockRiskManager.sol": {"keccak256": "0x6f8cbdaff1f0436750b2a61cf7def9d279416c778c57e21f90e500e9b99da02a", "urls": ["bzz-raw://9dbc697b4328da1f086bb90fe643a685f55d076727589df2509a883e4668876d", "dweb:/ipfs/QmdK5nkndDqnwb6yoPd1WkhpNs4R1gSrXQpksVPBXbGbe5"], "license": "BUSL-1.1"}, "test/mocks/MockVault.sol": {"keccak256": "0x327ec95cd13ea081a9173536f5089054930f007988aece68b3bfeb10e6f3badf", "urls": ["bzz-raw://34f731b9c01dc8b118a3a21724c6c0c74a0fd19eb926c8665cd35a5802b00cc2", "dweb:/ipfs/QmR5rv86qKYWVQcxU3oveeXWZVq5wbN1dWY59h2RoNHtyD"], "license": "BUSL-1.1"}}, "version": 1}, "id": 149}